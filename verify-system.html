<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Verification - Aanabi Pharmacy v2.0</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .verification-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .test-result {
            padding: 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .test-info {
            background: #e3f2fd;
            color: #0d47a1;
            border: 1px solid #bbdefb;
        }

        .demo-section {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }

        .mode-demo {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .mode-demo.staff {
            background: #3498db;
            color: white;
        }

        .mode-demo.admin {
            background: #e74c3c;
            color: white;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .feature-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .feature-item.staff-feature {
            border-left: 4px solid #3498db;
        }

        .feature-item.admin-feature {
            border-left: 4px solid #e74c3c;
        }

        .admin-only .feature-item.admin-feature {
            background: #fff5f5;
        }

        .staff-only .feature-item.staff-feature {
            background: #f5f9ff;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-online { background-color: #27ae60; }
        .status-offline { background-color: #e74c3c; }
        .status-warning { background-color: #f39c12; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                🏥 Aanabi Pharmacy v2.0
                <span style="font-size: 0.7rem; opacity: 0.8; margin-left: 0.5rem;">System Verification</span>
            </div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
                
                <!-- User Info -->
                <div class="user-info" id="user-info">
                    <div class="user-avatar" id="user-avatar">S</div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Staff Member</div>
                        <div class="user-role" id="user-role">Staff</div>
                    </div>
                    <span class="mode-indicator staff" id="mode-indicator">Staff Mode</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="pages/medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="pages/inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="pages/sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item admin-only"><a href="pages/purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="pages/customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item admin-only"><a href="pages/suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="pages/reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item admin-only"><a href="pages/settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="verification-card">
            <h1>🔍 System Verification & Testing</h1>
            <p>This page verifies that the simplified authentication system is working correctly.</p>
        </div>

        <!-- System Status -->
        <div class="verification-card">
            <h2>📊 System Status</h2>
            <div id="system-status">
                <div class="test-info">Checking system components...</div>
            </div>
        </div>

        <!-- Current Mode Demo -->
        <div class="verification-card">
            <h2>🔐 Current Mode</h2>
            <div class="demo-section">
                <div class="mode-demo" id="current-mode-display">Staff Mode</div>
                <p id="mode-description">You are currently in Staff mode. Some features are hidden.</p>
                
                <div style="margin-top: 1rem;">
                    <button class="btn btn-primary staff-only" onclick="testSwitchToAdmin()">
                        🔓 Test Switch to Admin Mode
                    </button>
                    <button class="btn btn-warning admin-only" onclick="testSwitchToStaff()">
                        🔒 Test Switch to Staff Mode  
                    </button>
                </div>
            </div>
        </div>

        <!-- Feature Visibility Test -->
        <div class="verification-card">
            <h2>👁️ Feature Visibility Test</h2>
            <p>The following features should show/hide based on your current mode:</p>
            
            <div class="feature-grid">
                <div class="feature-item staff-feature">
                    <h4>📊 Dashboard</h4>
                    <p>✅ Always Visible</p>
                    <small>Available in both modes</small>
                </div>
                
                <div class="feature-item staff-feature">
                    <h4>💊 Medicines</h4>
                    <p>✅ Always Visible</p>
                    <small>Available in both modes</small>
                </div>
                
                <div class="feature-item staff-feature">
                    <h4>💳 Sales</h4>
                    <p>✅ Always Visible</p>
                    <small>Available in both modes</small>
                </div>
                
                <div class="feature-item admin-feature admin-only">
                    <h4>🛒 Purchase</h4>
                    <p>🔒 Admin Only</p>
                    <small>Visible only in Admin mode</small>
                </div>
                
                <div class="feature-item admin-feature admin-only">
                    <h4>🏭 Suppliers</h4>
                    <p>🔒 Admin Only</p>
                    <small>Visible only in Admin mode</small>
                </div>
                
                <div class="feature-item admin-feature admin-only">
                    <h4>⚙️ Settings</h4>
                    <p>🔒 Admin Only</p>
                    <small>Visible only in Admin mode</small>
                </div>
            </div>
        </div>

        <!-- Role-Based Elements Test -->
        <div class="verification-card">
            <h2>🎭 Role-Based Elements Test</h2>
            
            <!-- Staff Only Section -->
            <div class="staff-only">
                <div class="alert alert-info">
                    <strong>📘 Staff Mode Active:</strong> You can see this section because you're in Staff mode.
                    This section is hidden when in Admin mode.
                </div>
            </div>
            
            <!-- Admin Only Section -->
            <div class="admin-only">
                <div class="alert alert-warning">
                    <strong>👑 Admin Mode Active:</strong> You can see this section because you're in Admin mode.
                    This section is hidden when in Staff mode.
                </div>
            </div>
            
            <div class="alert alert-success">
                <strong>✅ Always Visible:</strong> This section is visible in both Staff and Admin modes.
            </div>
        </div>

        <!-- Navigation Test -->
        <div class="verification-card">
            <h2>🧭 Navigation Test</h2>
            <p>Check the navigation menu above. Admin-only items should be:</p>
            <div class="staff-only">
                <div class="test-info">❌ <strong>Hidden</strong> - You're in Staff mode, so Purchase, Suppliers, and Settings should be hidden in the navigation.</div>
            </div>
            <div class="admin-only">
                <div class="test-success">✅ <strong>Visible</strong> - You're in Admin mode, so all navigation items should be visible.</div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="verification-card">
            <h2>🧪 Automated Test Results</h2>
            <div id="test-results">
                <div class="test-info">Running automated tests...</div>
            </div>
        </div>

        <!-- Manual Testing Instructions -->
        <div class="verification-card">
            <h2>📋 Manual Testing Checklist</h2>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                <h4>✅ Things to Test:</h4>
                <ol>
                    <li>🔄 Switch between Staff and Admin modes using the buttons above</li>
                    <li>👀 Verify that navigation items show/hide correctly</li>
                    <li>🎨 Check that header color changes between modes (blue → red)</li>
                    <li>📊 Confirm that dashboard shows correct mode in header</li>
                    <li>⚙️ Visit Settings page and test mode switching there</li>
                    <li>🔐 Verify admin password prompt works (password: admin123)</li>
                    <li>💾 Check that mode persists after page refresh</li>
                </ol>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="verification-card">
            <h2>🚀 Quick Actions</h2>
            <div class="text-center">
                <a href="index.html" class="btn btn-primary">🏠 Go to Dashboard</a>
                <a href="pages/settings.html" class="btn btn-success admin-only">⚙️ Go to Settings</a>
                <button class="btn btn-warning" onclick="resetSystem()">🔄 Reset System</button>
                <button class="btn btn-secondary" onclick="runFullTest()">🧪 Run Full Test</button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div>&copy; 2025 Aanabi Pharmacy Management System v2.0 - Simplified Edition</div>
    </footer>

    <!-- Scripts -->
    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    
    <script>
        async function initializeVerification() {
            try {
                console.log('🔍 Starting system verification...');
                
                // Wait for systems
                await waitForSystems();
                
                // Setup interface
                setupInterface();
                
                // Run automated tests
                await runAutomatedTests();
                
                // Update display
                updateDateTime();
                setInterval(updateDateTime, 1000);
                
                console.log('✅ System verification complete');
                
            } catch (error) {
                console.error('❌ Verification failed:', error);
                showSystemStatus('error', 'System verification failed: ' + error.message);
            }
        }

        async function waitForSystems() {
            let attempts = 0;
            const maxAttempts = 100;

            while ((!window.dbManager?.isReady() || !window.authManager) && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
                if (attempts % 20 === 0) {
                    showSystemStatus('warning', `Waiting for systems... (${attempts/10}s)`);
                }
            }

            if (!window.dbManager?.isReady() || !window.authManager) {
                throw new Error('Systems failed to initialize within timeout');
            }
        }

        function setupInterface() {
            const user = authManager.getCurrentUser();
            
            // Update user info
            document.getElementById('user-avatar').textContent = user.role === 'admin' ? 'A' : 'S';
            document.getElementById('user-name').textContent = user.full_name;
            document.getElementById('user-role').textContent = user.role.charAt(0).toUpperCase() + user.role.slice(1);
            
            // Update mode indicator
            const modeIndicator = document.getElementById('mode-indicator');
            const currentModeDisplay = document.getElementById('current-mode-display');
            const modeDescription = document.getElementById('mode-description');
            
            modeIndicator.className = `mode-indicator ${user.role}`;
            modeIndicator.textContent = `${user.role.charAt(0).toUpperCase() + user.role.slice(1)} Mode`;
            
            currentModeDisplay.className = `mode-demo ${user.role}`;
            currentModeDisplay.textContent = `${user.role.charAt(0).toUpperCase() + user.role.slice(1)} Mode`;
            
            if (user.role === 'admin') {
                modeDescription.textContent = 'You are currently in Admin mode. All features are visible.';
            } else {
                modeDescription.textContent = 'You are currently in Staff mode. Some features are hidden.';
            }

            // Set body class for role-based styling
            document.body.className = user.role;
            
            showSystemStatus('success', `Interface setup complete for ${user.role} mode`);
        }

        async function runAutomatedTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="test-info">Running automated tests...</div>';
            
            const tests = [];
            
            try {
                // Test 1: Database connection
                const medicines = await dbManager.getAll('medicines');
                tests.push({
                    name: 'Database Connection',
                    success: true,
                    message: `✅ Database accessible, found ${medicines.length} medicines`
                });
            } catch (error) {
                tests.push({
                    name: 'Database Connection',
                    success: false,
                    message: `❌ Database error: ${error.message}`
                });
            }
            
            // Test 2: Auth manager
            try {
                const user = authManager.getCurrentUser();
                tests.push({
                    name: 'Authentication System',
                    success: true,
                    message: `✅ Auth working, current user: ${user.username} (${user.role})`
                });
            } catch (error) {
                tests.push({
                    name: 'Authentication System',
                    success: false,
                    message: `❌ Auth error: ${error.message}`
                });
            }
            
            // Test 3: Role-based CSS
            const adminElements = document.querySelectorAll('.admin-only');
            const staffElements = document.querySelectorAll('.staff-only');
            tests.push({
                name: 'Role-Based Elements',
                success: true,
                message: `✅ Found ${adminElements.length} admin-only and ${staffElements.length} staff-only elements`
            });
            
            // Test 4: Mode switching capability
            try {
                const canSwitchModes = typeof authManager.switchToAdminMode === 'function' && 
                                      typeof authManager.switchToStaffMode === 'function';
                tests.push({
                    name: 'Mode Switching',
                    success: canSwitchModes,
                    message: canSwitchModes ? '✅ Mode switching functions available' : '❌ Mode switching functions missing'
                });
            } catch (error) {
                tests.push({
                    name: 'Mode Switching',
                    success: false,
                    message: `❌ Mode switching error: ${error.message}`
                });
            }
            
            // Display results
            let html = '';
            let allPassed = true;
            
            tests.forEach(test => {
                const cssClass = test.success ? 'test-success' : 'test-error';
                html += `<div class="${cssClass}">${test.name}: ${test.message}</div>`;
                if (!test.success) allPassed = false;
            });
            
            if (allPassed) {
                html += '<div class="test-success"><strong>🎉 All tests passed! System is working correctly.</strong></div>';
                showSystemStatus('success', 'All automated tests passed');
            } else {
                html += '<div class="test-error"><strong>⚠️ Some tests failed. Please check the issues above.</strong></div>';
                showSystemStatus('error', 'Some automated tests failed');
            }
            
            results.innerHTML = html;
        }

        async function testSwitchToAdmin() {
            try {
                const result = await authManager.switchToAdminMode();
                if (result.success) {
                    alert('✅ Successfully switched to Admin mode!');
                    window.location.reload();
                } else {
                    alert('❌ Failed to switch: ' + result.message);
                }
            } catch (error) {
                alert('❌ Error: ' + error.message);
            }
        }

        async function testSwitchToStaff() {
            try {
                const result = await authManager.switchToStaffMode();
                if (result.success) {
                    alert('✅ Successfully switched to Staff mode!');
                    window.location.reload();
                } else {
                    alert('❌ Failed to switch: ' + result.message);
                }
            } catch (error) {
                alert('❌ Error: ' + error.message);
            }
        }

        function resetSystem() {
            if (confirm('This will reset the system to default settings and reload the page. Continue?')) {
                localStorage.clear();
                sessionStorage.clear();
                window.location.reload();
            }
        }

        async function runFullTest() {
            try {
                await runAutomatedTests();
                alert('🧪 Full test completed! Check the results above.');
            } catch (error) {
                alert('❌ Test failed: ' + error.message);
            }
        }

        function showSystemStatus(type, message) {
            const statusDiv = document.getElementById('system-status');
            const cssClass = type === 'success' ? 'test-success' : 
                           type === 'error' ? 'test-error' : 'test-info';
            
            const indicator = type === 'success' ? '✅' : 
                            type === 'error' ? '❌' : 'ℹ️';
            
            statusDiv.innerHTML = `<div class="${cssClass}">${indicator} ${message}</div>`;
        }

        function updateDateTime() {
            const now = new Date();
            const dateOptions = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            const timeOptions = { 
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit' 
            };

            const dateElement = document.getElementById('current-date');
            const timeElement = document.getElementById('current-time');
            
            if (dateElement && timeElement) {
                dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
                timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeVerification);
    </script>
</body>
</html>