<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Customer Management - Aanabi Pharmacy v2.0</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* Enhanced Customer Management Styles */
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            font-size: 0.9rem;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.8;
            font-size: 0.8rem;
            text-transform: capitalize;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        /* Enhanced Statistics Cards */
        .enhanced-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .enhanced-stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .enhanced-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .enhanced-stat-card.customers {
            border-left-color: #3498db;
        }

        .enhanced-stat-card.loyalty {
            border-left-color: #f39c12;
        }

        .enhanced-stat-card.medical {
            border-left-color: #e74c3c;
        }

        .enhanced-stat-card.revenue {
            border-left-color: #27ae60;
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-icon {
            font-size: 1.5rem;
        }

        .enhanced-stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-subtitle {
            font-size: 0.8rem;
            color: #95a5a6;
        }

        /* Enhanced Search and Filters */
        .search-filters {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 1rem;
            align-items: end;
        }

        /* Enhanced Table Styles */
        .enhanced-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .enhanced-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 1rem;
            border-bottom: 2px solid #dee2e6;
            font-size: 0.85rem;
        }

        .enhanced-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.85rem;
            vertical-align: middle;
        }

        .enhanced-table tr:hover {
            background: #f8f9fa;
        }

        /* Customer Profile Enhancement */
        .customer-profile {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .customer-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .customer-contact {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .customer-id {
            font-size: 0.75rem;
            color: #95a5a6;
        }

        /* Medical Information Enhancement */
        .medical-info {
            max-width: 200px;
        }

        .medical-conditions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            margin-bottom: 0.25rem;
            font-size: 0.75rem;
            display: inline-block;
        }

        .allergies {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            display: inline-block;
        }

        .no-medical-info {
            color: #95a5a6;
            font-style: italic;
            font-size: 0.8rem;
        }

        /* Loyalty Points Display */
        .loyalty-display {
            text-align: center;
        }

        .loyalty-points {
            font-size: 1.2rem;
            font-weight: bold;
            color: #f39c12;
        }

        .loyalty-value {
            font-size: 0.75rem;
            color: #7f8c8d;
        }

        .loyalty-tier {
            background: #f39c12;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            text-transform: uppercase;
            margin-top: 0.25rem;
        }

        /* Status Indicators */
        .status-indicator {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-vip {
            background: #fff3cd;
            color: #856404;
        }

        /* Enhanced Modal Styles */
        .modal-xxl {
            max-width: 1200px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #3498db;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Medical Records Section */
        .medical-section {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-left-color: #e74c3c;
        }

        .medical-section .section-title {
            color: #c0392b;
        }

        /* Loyalty Section */
        .loyalty-section {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-left-color: #f39c12;
        }

        .loyalty-section .section-title {
            color: #d35400;
        }

        /* Medical History Enhancement */
        .medical-history-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
        }

        .medical-field {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #dee2e6;
        }

        .medical-field label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .medical-field textarea {
            min-height: 80px;
            resize: vertical;
        }

        /* Loyalty Points Management */
        .loyalty-management {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .loyalty-stat {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .loyalty-stat-value {
            font-size: 1.3rem;
            font-weight: bold;
            color: #f39c12;
            margin-bottom: 0.5rem;
        }

        .loyalty-stat-label {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* Action Buttons Enhancement */
        .action-buttons {
            display: flex;
            gap: 0.25rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
        }

        /* Medical Alert Indicators */
        .medical-alert {
            background: #f8d7da;
            color: #721c24;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-block;
            margin-right: 0.25rem;
        }

        /* Customer Details Modal Enhancement */
        .customer-details-section {
            margin-bottom: 2rem;
        }

        .details-header {
            background: #3498db;
            color: white;
            padding: 1rem;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }

        .details-body {
            background: white;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0 0 8px 8px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #495057;
        }

        .detail-value {
            color: #2c3e50;
        }

        /* Permission-based hiding */
        .admin-only {
            display: none;
        }

        body.admin .admin-only {
            display: block;
        }

        body.admin .admin-only.inline {
            display: inline-block;
        }

        body.admin .admin-only.flex {
            display: flex;
        }

        /* Loading states */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            color: #2c3e50;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .enhanced-stats {
                grid-template-columns: 1fr;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
            
            .medical-history-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>👥 Aanabi Customers</h3>
            <p>Loading enhanced customer management...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                🏥 Aanabi Pharmacy v2.0
                <span style="font-size: 0.7rem; opacity: 0.8; margin-left: 0.5rem;">Enhanced Customer Management</span>
            </div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
                
                <!-- User Info -->
                <div class="user-info" id="user-info" style="display: none;">
                    <div class="user-avatar" id="user-avatar">A</div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Admin</div>
                        <div class="user-role" id="user-role">Administrator</div>
                    </div>
                    <button class="logout-btn" onclick="handleLogout()">🚪 Logout</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item admin-only"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link active">👥 Customers</a></li>
                <li class="nav-item admin-only"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item admin-only"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">👥 Enhanced Customer Management</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <p class="text-muted">Comprehensive customer management with medical records and loyalty program integration</p>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-primary" onclick="showEnhancedAddCustomerModal()">➕ Add Customer</button>
                        <button class="btn btn-success admin-only" onclick="exportEnhancedCustomers()">📤 Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Customer Statistics with Medical & Loyalty Data -->
        <div class="enhanced-stats">
            <div class="enhanced-stat-card customers">
                <div class="stat-header">
                    <span class="stat-title">Total Customers</span>
                    <span class="stat-icon">👥</span>
                </div>
                <div class="enhanced-stat-value" id="total-customers">0</div>
                <div class="stat-subtitle" id="active-customers">0 active customers</div>
            </div>

            <div class="enhanced-stat-card loyalty">
                <div class="stat-header">
                    <span class="stat-title">Loyalty Members</span>
                    <span class="stat-icon">🏆</span>
                </div>
                <div class="enhanced-stat-value" id="loyalty-customers">0</div>
                <div class="stat-subtitle" id="total-loyalty-points">0 total points</div>
            </div>

            <div class="enhanced-stat-card medical">
                <div class="stat-header">
                    <span class="stat-title">Medical Records</span>
                    <span class="stat-icon">🏥</span>
                </div>
                <div class="enhanced-stat-value" id="customers-with-medical-records">0</div>
                <div class="stat-subtitle">Customers with medical data</div>
            </div>

            <div class="enhanced-stat-card revenue">
                <div class="stat-header">
                    <span class="stat-title">Total Revenue</span>
                    <span class="stat-icon">💰</span>
                </div>
                <div class="enhanced-stat-value" id="total-customer-revenue">Rs. 0</div>
                <div class="stat-subtitle">From all customers</div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Search and Filter Section -->
        <div class="search-filters">
            <div class="filter-row">
                <div class="form-group">
                    <label class="form-label">Search Customers</label>
                    <input type="text" id="customer-search" class="form-input" 
                           placeholder="Search by name, phone, email, or medical conditions...">
                </div>
                <div class="form-group">
                    <label class="form-label">Status Filter</label>
                    <select id="status-filter" class="form-select" onchange="filterCustomers()">
                        <option value="">All Status</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                        <option value="VIP">VIP</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Loyalty Filter</label>
                    <select id="loyalty-filter" class="form-select" onchange="filterCustomers()">
                        <option value="">All Customers</option>
                        <option value="with-points">Has Loyalty Points</option>
                        <option value="no-points">No Loyalty Points</option>
                        <option value="high-value">High Value (1000+ pts)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Medical Filter</label>
                    <select id="medical-filter" class="form-select" onchange="filterCustomers()">
                        <option value="">All Medical</option>
                        <option value="has-conditions">Has Medical Conditions</option>
                        <option value="has-allergies">Has Allergies</option>
                        <option value="has-history">Has Medical History</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-secondary" onclick="clearCustomerFilters()">🔄 Clear</button>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Customer List with Medical Records & Loyalty -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📋 Enhanced Customer Database</h3>
                <small class="text-muted">Complete customer profiles with medical records and loyalty program data</small>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Customer Profile</th>
                                <th>Medical Information</th>
                                <th>🏆 Loyalty Status</th>
                                <th>Purchase History</th>
                                <th>Account Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="enhanced-customers-tbody">
                            <!-- Enhanced customer data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- ✨ ENHANCED: Add/Edit Customer Modal with Medical Records -->
    <div id="enhanced-customer-modal" class="modal-overlay" style="display: none;">
        <div class="modal modal-xxl">
            <div class="modal-header">
                <h3 class="modal-title" id="enhanced-customer-modal-title">👥 Add New Customer</h3>
                <button class="modal-close" onclick="closeEnhancedCustomerModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="enhanced-customer-form">
                    <input type="hidden" id="enhanced-customer-id" name="customer_id">
                    
                    <!-- ✨ Personal Information Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <span>👤</span> Personal Information
                        </h4>
                        <div class="row">
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">Customer Name *</label>
                                    <input type="text" id="enhanced-customer-name" name="customer_name" class="form-input" required>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">Phone Number *</label>
                                    <input type="tel" id="enhanced-phone-number" name="phone_number" class="form-input" required>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">Email Address</label>
                                    <input type="email" id="enhanced-email" name="email" class="form-input">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">Date of Birth</label>
                                    <input type="date" id="enhanced-date-of-birth" name="date_of_birth" class="form-input">
                                </div>
                            </div>
                            <div class="col-8">
                                <div class="form-group">
                                    <label class="form-label">Address</label>
                                    <textarea id="enhanced-address" name="address" class="form-textarea" rows="2" 
                                              placeholder="Complete address with city and postal code"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Enhanced Medical Records Section -->
                    <div class="form-section medical-section">
                        <h4 class="section-title">
                            <span>🏥</span> Comprehensive Medical Records
                        </h4>
                        <div class="medical-history-grid">
                            <div class="medical-field">
                                <label class="form-label">📋 Medical History</label>
                                <textarea id="enhanced-medical-history" name="medical_history" class="form-textarea" 
                                          placeholder="Previous surgeries, hospitalizations, family history...&#10;&#10;Example:&#10;- Heart bypass surgery (2019)&#10;- Chronic hypertension&#10;- Family history of diabetes"></textarea>
                                <small class="text-muted">Complete medical background and family history</small>
                            </div>
                            <div class="medical-field">
                                <label class="form-label">🩺 Current Diagnosis</label>
                                <textarea id="enhanced-diagnosis" name="diagnosis" class="form-textarea" 
                                          placeholder="Current active medical conditions...&#10;&#10;Example:&#10;- Essential Hypertension (ongoing treatment)&#10;- Type 2 Diabetes Mellitus&#10;- Mild kidney disease (monitoring)"></textarea>
                                <small class="text-muted">Active conditions and ongoing treatments</small>
                            </div>
                            <div class="medical-field">
                                <label class="form-label">⚠️ ADR History</label>
                                <textarea id="enhanced-adr-history" name="adr_history" class="form-textarea" 
                                          placeholder="Adverse Drug Reactions...&#10;&#10;Example:&#10;- Penicillin: Severe allergic reaction&#10;- Aspirin: Gastrointestinal bleeding&#10;- Metformin: Initial nausea (resolved)"></textarea>
                                <small class="text-muted">Previous adverse drug reactions for safety</small>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 1rem;">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">🚫 Known Allergies</label>
                                    <input type="text" id="enhanced-allergies" name="allergies" class="form-input" 
                                           placeholder="e.g., Penicillin, Aspirin, Shellfish">
                                    <small class="text-muted">Comma-separated list of allergies</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">📅 Last Medical Update</label>
                                    <input type="date" id="last-medical-update" name="last_medical_update" class="form-input">
                                    <small class="text-muted">When medical information was last updated</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Enhanced Loyalty Program Section -->
                    <div class="form-section loyalty-section">
                        <h4 class="section-title">
                            <span>🏆</span> Loyalty Program Management
                        </h4>
                        <div class="loyalty-management">
                            <div class="loyalty-stat">
                                <div class="loyalty-stat-value" id="current-loyalty-points">0</div>
                                <div class="loyalty-stat-label">Current Points</div>
                            </div>
                            <div class="loyalty-stat">
                                <div class="loyalty-stat-value" id="loyalty-value-display">Rs. 0</div>
                                <div class="loyalty-stat-label">Points Value</div>
                            </div>
                            <div class="loyalty-stat">
                                <div class="loyalty-stat-value" id="loyalty-tier-display">Member</div>
                                <div class="loyalty-stat-label">Tier Status</div>
                            </div>
                            <div class="loyalty-stat">
                                <div class="loyalty-stat-value" id="total-earned-points">0</div>
                                <div class="loyalty-stat-label">Total Earned</div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 1rem;">
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">🏆 Current Loyalty Points</label>
                                    <input type="number" id="enhanced-loyalty-points" name="loyalty_points" class="form-input" 
                                           value="0" min="0" onchange="calculateLoyaltyValue()">
                                    <small class="text-muted">Available points balance</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">📈 Points Adjustment</label>
                                    <input type="number" id="points-adjustment" class="form-input" 
                                           placeholder="±0" onchange="adjustLoyaltyPoints()">
                                    <small class="text-muted">Add or subtract points (±)</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <label class="form-label">💭 Adjustment Reason</label>
                                    <input type="text" id="adjustment-reason" class="form-input" 
                                           placeholder="Reason for adjustment">
                                    <small class="text-muted">Why points were adjusted</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Account Information Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <span>⚙️</span> Account Information
                        </h4>
                        <div class="row">
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Account Status</label>
                                    <select id="enhanced-status" name="status" class="form-select">
                                        <option value="Active">✅ Active</option>
                                        <option value="Inactive">⏸️ Inactive</option>
                                        <option value="VIP">⭐ VIP Customer</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Total Purchase Amount</label>
                                    <input type="number" id="enhanced-total-purchase-amount" name="total_purchase_amount" 
                                           class="form-input" step="0.01" value="0" readonly>
                                    <small class="text-muted">Lifetime purchase value</small>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Registration Date</label>
                                    <input type="date" id="enhanced-registration-date" name="registration_date" class="form-input">
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="form-group">
                                    <label class="form-label">Last Purchase Date</label>
                                    <input type="date" id="enhanced-last-purchase-date" name="last_purchase_date" class="form-input" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Form Actions -->
                    <div style="text-align: center; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #dee2e6;">
                        <button type="submit" class="btn btn-primary btn-lg" style="padding: 1rem 2rem;">
                            👥 Save Enhanced Customer
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeEnhancedCustomerModal()" style="margin-left: 1rem;">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- ✨ Enhanced Customer Details Modal -->
    <div id="enhanced-customer-details-modal" class="modal-overlay" style="display: none;">
        <div class="modal modal-xxl">
            <div class="modal-header">
                <h3 class="modal-title">👥 Complete Customer Profile</h3>
                <button class="modal-close" onclick="closeEnhancedCustomerDetailsModal()">&times;</button>
            </div>
            <div class="modal-body" id="enhanced-customer-details-content">
                <!-- Enhanced customer details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/audit.js"></script>
    <script src="../js/notifications.js"></script>
    <script src="../js/customers.js"></script>
    
    <script>
        // ✨ Enhanced Customer Management Initialization
        async function initializeEnhancedCustomers() {
            try {
                console.log('👥 Enhanced customer management initialization started...');
                
                // Wait for systems to be ready
                await waitForSystems();
                
                // Check authentication
                if (!authManager.isLoggedIn()) {
                    authManager.redirectToLogin('Please login to access customer management');
                    return;
                }
                
                // Setup user interface
                setupUserInterface();
                
                // Initialize enhanced customer system
                await initializeEnhancedCustomerSystem();
                
                // Hide loading overlay
                document.getElementById('loading-overlay').style.display = 'none';
                
                console.log('✅ Enhanced customer management initialized successfully');
                
            } catch (error) {
                console.error('❌ Customer management initialization failed:', error);
                showError('Failed to initialize customer management system');
            }
        }

        async function waitForSystems() {
            let attempts = 0;
            const maxAttempts = 100;

            while ((!dbManager?.isReady() || !authManager) && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!dbManager?.isReady() || !authManager) {
                throw new Error('Systems failed to initialize');
            }
        }

        function setupUserInterface() {
            const user = authManager.getCurrentUser();
            if (!user) return;

            // Update user info display
            document.getElementById('user-avatar').textContent = user.full_name.charAt(0).toUpperCase();
            document.getElementById('user-name').textContent = user.full_name;
            document.getElementById('user-role').textContent = user.role;
            document.getElementById('user-info').style.display = 'flex';

            // Set role-based styling
            document.body.className = user.role;

            console.log(`👥 Enhanced customer management loaded for ${user.full_name} (${user.role})`);
        }

        async function initializeEnhancedCustomerSystem() {
            // Load enhanced statistics
            await loadEnhancedCustomerStatistics();
            
            // Load customers table
            await loadEnhancedCustomersTable();
            
            // Setup enhanced event listeners
            setupEnhancedEventListeners();
        }

        async function loadEnhancedCustomerStatistics() {
            try {
                const customers = await dbManager.getAll('customers');
                
                const totalCustomers = customers.length;
                const activeCustomers = customers.filter(c => c.status === 'Active').length;
                const loyaltyCustomers = customers.filter(c => (c.loyalty_points || 0) > 0).length;
                const customersWithMedicalRecords = customers.filter(c => 
                    c.medical_history || c.diagnosis || c.adr_history || c.allergies
                ).length;
                
                const totalLoyaltyPoints = customers.reduce((sum, c) => sum + (c.loyalty_points || 0), 0);
                const totalRevenue = customers.reduce((sum, c) => sum + (c.total_purchase_amount || 0), 0);
                
                // Update enhanced statistics
                document.getElementById('total-customers').textContent = totalCustomers.toLocaleString();
                document.getElementById('active-customers').textContent = `${activeCustomers} active customers`;
                document.getElementById('loyalty-customers').textContent = loyaltyCustomers.toLocaleString();
                document.getElementById('total-loyalty-points').textContent = `${totalLoyaltyPoints.toLocaleString()} total points`;
                document.getElementById('customers-with-medical-records').textContent = customersWithMedicalRecords.toLocaleString();
                document.getElementById('total-customer-revenue').textContent = `Rs. ${totalRevenue.toLocaleString()}`;
                
                console.log(`📊 Customer stats: ${totalCustomers} total, ${loyaltyCustomers} loyalty, ${customersWithMedicalRecords} with medical records`);
                
            } catch (error) {
                console.error('Error loading enhanced customer statistics:', error);
            }
        }

        async function loadEnhancedCustomersTable() {
            try {
                const customers = await dbManager.getAll('customers');
                const tbody = document.getElementById('enhanced-customers-tbody');
                
                if (!tbody) return;
                
                tbody.innerHTML = '';
                
                customers.forEach(customer => {
                    const row = document.createElement('tr');
                    
                    // Customer profile column
                    const customerProfile = `
                        <div class="customer-profile">
                            <div class="customer-name">${customer.customer_name}</div>
                            <div class="customer-contact">📞 ${customer.phone_number}</div>
                            <div class="customer-contact">✉️ ${customer.email || 'N/A'}</div>
                            <div class="customer-id">${customer.customer_id}</div>
                        </div>
                    `;
                    
                    // Medical information column
                    const medicalInfo = `
                        <div class="medical-info">
                            ${customer.allergies ? `<div class="allergies">🚫 ${customer.allergies}</div>` : ''}
                            ${customer.medical_history || customer.diagnosis ? `<div class="medical-conditions">🏥 Has Medical Records</div>` : ''}
                            ${customer.adr_history ? `<div class="medical-alert">⚠️ ADR History</div>` : ''}
                            ${!customer.allergies && !customer.medical_history && !customer.diagnosis && !customer.adr_history ? 
                                '<div class="no-medical-info">No medical data</div>' : ''}
                        </div>
                    `;
                    
                    // Loyalty status column
                    const loyaltyPoints = customer.loyalty_points || 0;
                    const loyaltyValue = loyaltyPoints * 0.10; // 1 point = Rs. 0.10
                    const loyaltyTier = loyaltyPoints >= 1000 ? 'Gold' : loyaltyPoints >= 500 ? 'Silver' : 'Member';
                    
                    const loyaltyStatus = `
                        <div class="loyalty-display">
                            <div class="loyalty-points">${loyaltyPoints.toLocaleString()}</div>
                            <div class="loyalty-value">Rs. ${loyaltyValue.toFixed(2)} value</div>
                            <div class="loyalty-tier">${loyaltyTier}</div>
                        </div>
                    `;
                    
                    // Purchase history column
                    const purchaseHistory = `
                        <div>
                            <div><strong>Rs. ${(customer.total_purchase_amount || 0).toLocaleString()}</strong></div>
                            <small class="text-muted">Total purchases</small><br>
                            <small class="text-muted">Last: ${customer.last_purchase_date || 'Never'}</small>
                        </div>
                    `;
                    
                    // Status column
                    const statusClass = `status-${customer.status.toLowerCase()}`;
                    const statusBadge = `<span class="status-indicator ${statusClass}">${customer.status}</span>`;
                    
                    // Actions column
                    const actions = `
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-primary btn-icon" onclick="editEnhancedCustomer('${customer.customer_id}')" title="Edit Customer">✏️</button>
                            <button class="btn btn-sm btn-info btn-icon" onclick="viewEnhancedCustomerDetails('${customer.customer_id}')" title="View Details">👁️</button>
                            <button class="btn btn-sm btn-success btn-icon" onclick="viewPurchaseHistory('${customer.customer_id}')" title="Purchase History">📋</button>
                            <button class="btn btn-sm btn-warning btn-icon" onclick="manageLoyaltyPoints('${customer.customer_id}')" title="Manage Loyalty">🏆</button>
                            <button class="btn btn-sm btn-danger btn-icon admin-only" onclick="deleteCustomer('${customer.customer_id}')" title="Delete Customer">🗑️</button>
                        </div>
                    `;
                    
                    row.innerHTML = `
                        <td>${customerProfile}</td>
                        <td>${medicalInfo}</td>
                        <td>${loyaltyStatus}</td>
                        <td>${purchaseHistory}</td>
                        <td>${statusBadge}</td>
                        <td>${actions}</td>
                    `;
                    
                    tbody.appendChild(row);
                });
                
            } catch (error) {
                console.error('Error loading enhanced customers table:', error);
            }
        }

        function setupEnhancedEventListeners() {
            // Enhanced customer form
            const customerForm = document.getElementById('enhanced-customer-form');
            if (customerForm) {
                customerForm.addEventListener('submit', handleEnhancedCustomerSubmit);
            }
            
            // Search functionality
            const searchInput = document.getElementById('customer-search');
            if (searchInput) {
                searchInput.addEventListener('input', handleCustomerSearch);
            }
            
            // Loyalty points calculation
            const loyaltyInput = document.getElementById('enhanced-loyalty-points');
            if (loyaltyInput) {
                loyaltyInput.addEventListener('input', calculateLoyaltyValue);
            }
        }

        // Enhanced calculation functions
        function calculateLoyaltyValue() {
            const loyaltyPoints = parseInt(document.getElementById('enhanced-loyalty-points').value) || 0;
            const loyaltyValue = loyaltyPoints * 0.10; // 1 point = Rs. 0.10
            const loyaltyTier = loyaltyPoints >= 1000 ? 'Gold' : loyaltyPoints >= 500 ? 'Silver' : 'Member';
            
            // Update loyalty displays
            document.getElementById('current-loyalty-points').textContent = loyaltyPoints.toLocaleString();
            document.getElementById('loyalty-value-display').textContent = `Rs. ${loyaltyValue.toFixed(2)}`;
            document.getElementById('loyalty-tier-display').textContent = loyaltyTier;
        }

        function adjustLoyaltyPoints() {
            const currentPoints = parseInt(document.getElementById('enhanced-loyalty-points').value) || 0;
            const adjustment = parseInt(document.getElementById('points-adjustment').value) || 0;
            const newPoints = Math.max(0, currentPoints + adjustment);
            
            document.getElementById('enhanced-loyalty-points').value = newPoints;
            calculateLoyaltyValue();
            
            // Clear adjustment field
            document.getElementById('points-adjustment').value = '';
        }

        // Modal functions
        function showEnhancedAddCustomerModal() {
            document.getElementById('enhanced-customer-modal').style.display = 'flex';
            document.getElementById('enhanced-customer-modal-title').textContent = '👥 Add New Customer';
            document.getElementById('enhanced-customer-form').reset();
            
            // Set defaults
            document.getElementById('enhanced-loyalty-points').value = '0';
            document.getElementById('enhanced-registration-date').value = new Date().toISOString().split('T')[0];
            calculateLoyaltyValue();
        }

        function closeEnhancedCustomerModal() {
            document.getElementById('enhanced-customer-modal').style.display = 'none';
        }

        function closeEnhancedCustomerDetailsModal() {
            document.getElementById('enhanced-customer-details-modal').style.display = 'none';
        }

        // Placeholder functions for enhanced features
        async function handleEnhancedCustomerSubmit(event) {
            event.preventDefault();
            console.log('👥 Enhanced customer form submitted');
            showAlert('Enhanced customer save functionality ready for implementation! 👥', 'info');
        }

        function handleCustomerSearch(event) {
            const searchTerm = event.target.value.toLowerCase();
            console.log('🔍 Searching customers:', searchTerm);
            // Search functionality will be implemented
        }

        function filterCustomers() {
            console.log('🔽 Filtering customers');
            // Filter functionality will be implemented
        }

        function clearCustomerFilters() {
            document.getElementById('status-filter').value = '';
            document.getElementById('loyalty-filter').value = '';
            document.getElementById('medical-filter').value = '';
            document.getElementById('customer-search').value = '';
            console.log('🔄 Customer filters cleared');
        }

        function editEnhancedCustomer(customerId) {
            console.log('✏️ Edit customer:', customerId);
            showAlert(`Enhanced edit functionality for ${customerId} ready for implementation! ✏️`, 'info');
        }

        function viewEnhancedCustomerDetails(customerId) {
            console.log('👁️ View customer details:', customerId);
            showAlert(`Enhanced customer details view for ${customerId} ready for implementation! 👁️`, 'info');
        }

        function viewPurchaseHistory(customerId) {
            console.log('📋 View purchase history:', customerId);
            showAlert(`Enhanced purchase history for ${customerId} ready for implementation! 📋`, 'info');
        }

        function manageLoyaltyPoints(customerId) {
            console.log('🏆 Manage loyalty points:', customerId);
            showAlert(`Enhanced loyalty points management for ${customerId} ready for implementation! 🏆`, 'info');
        }

        function deleteCustomer(customerId) {
            if (confirm('Are you sure you want to delete this customer? This will also delete their medical records and purchase history.')) {
                console.log('🗑️ Delete customer:', customerId);
                showAlert(`Enhanced delete functionality for ${customerId} ready for implementation! 🗑️`, 'info');
            }
        }

        function exportEnhancedCustomers() {
            console.log('📤 Export enhanced customers');
            showAlert('Enhanced customer export with medical records and loyalty data ready for implementation! 📤', 'info');
        }

        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                authManager.logout();
                window.location.href = 'login.html';
            }
        }

        function showError(message) {
            showAlert(message, 'error');
        }

        function showAlert(message, type) {
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(alert, mainContent.firstChild);

            setTimeout(() => alert.remove(), 5000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeEnhancedCustomers);
    </script>
</body>
</html>