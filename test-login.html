<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Login - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* Copy login styles from original but with debug additions */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .login-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .login-form {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .login-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #e53935;
        }

        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .info-message {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }

        .debug-info {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 0.8rem;
            font-family: monospace;
        }

        .system-status {
            text-align: center;
            margin: 1rem 0;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-online { background-color: #4caf50; }
        .status-offline { background-color: #f44336; }
        .status-waiting { background-color: #ff9800; }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Login Header -->
        <div class="login-header">
            <h1>🏥 Aanabi Pharmacy</h1>
            <p>Enhanced Login System v2.0</p>
        </div>

        <!-- Login Form -->
        <div class="login-form">
            <!-- System Status -->
            <div class="system-status">
                <span id="status-indicator" class="status-indicator status-offline"></span>
                <span id="status-text">Initializing...</span>
            </div>

            <!-- Messages -->
            <div id="message-container"></div>

            <!-- Debug Info -->
            <div id="debug-info" class="debug-info" style="display: none;">
                Debug: System initializing...
            </div>

            <form id="login-form" style="display: none;">
                <div class="form-group">
                    <label class="form-label" for="username">Username</label>
                    <input type="text" id="username" name="username" class="form-input" required autofocus value="admin">
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">Password</label>
                    <input type="password" id="password" name="password" class="form-input" required value="admin123">
                </div>

                <button type="submit" id="login-btn" class="login-btn">
                    <span id="login-text">Login</span>
                </button>
            </form>

            <!-- Quick Actions -->
            <div style="text-align: center; margin-top: 1rem;">
                <button onclick="toggleDebug()" style="padding: 5px 10px; margin: 5px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Toggle Debug
                </button>
                <button onclick="checkSystems()" style="padding: 5px 10px; margin: 5px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Check Systems
                </button>
                <button onclick="resetSystem()" style="padding: 5px 10px; margin: 5px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Reset System
                </button>
            </div>

            <!-- Demo Credentials -->
            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 1rem; margin-top: 1rem; font-size: 0.9rem;">
                <h4 style="margin: 0 0 0.5rem 0; color: #495057;">🔑 Demo Credentials</h4>
                <p style="margin: 0.25rem 0; color: #6c757d;"><strong>Admin:</strong> admin / admin123</p>
                <p style="margin: 0.25rem 0; color: #6c757d;"><strong>Staff:</strong> staff / staff123</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/auth.js"></script>
    
    <script>
        class EnhancedLoginPage {
            constructor() {
                this.debugMode = false;
                this.systemReady = false;
                this.init();
            }

            async init() {
                this.log('🚀 Starting enhanced login initialization...');
                this.updateStatus('waiting', 'Initializing systems...');
                
                try {
                    // Wait for systems with detailed progress
                    await this.waitForSystems();
                    
                    // Verify systems
                    await this.verifySystems();
                    
                    // Setup interface
                    this.setupEventListeners();
                    this.showLoginForm();
                    
                    this.systemReady = true;
                    this.updateStatus('online', 'System ready - you can login!');
                    this.showMessage('✅ System initialized successfully!', 'success');
                    
                } catch (error) {
                    this.updateStatus('offline', 'System failed to initialize');
                    this.showMessage(`❌ Initialization failed: ${error.message}`, 'error');
                    this.log('❌ Initialization failed: ' + error.message);
                }
            }

            async waitForSystems() {
                this.log('⏳ Waiting for database manager...');
                let attempts = 0;
                const maxAttempts = 100;

                while ((!window.dbManager) && attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                    if (attempts % 10 === 0) {
                        this.updateStatus('waiting', `Waiting for database... (${attempts/10}s)`);
                    }
                }

                if (!window.dbManager) {
                    throw new Error('Database manager failed to load');
                }
                this.log('✅ Database manager found');

                this.log('⏳ Waiting for database to be ready...');
                attempts = 0;
                while (!window.dbManager.isReady() && attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                    if (attempts % 10 === 0) {
                        this.updateStatus('waiting', `Database initializing... (${attempts/10}s)`);
                    }
                }

                if (!window.dbManager.isReady()) {
                    throw new Error('Database failed to initialize');
                }
                this.log('✅ Database is ready');

                this.log('⏳ Waiting for auth manager...');
                attempts = 0;
                while (!window.authManager && attempts < maxAttempts) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                    if (attempts % 10 === 0) {
                        this.updateStatus('waiting', `Waiting for auth... (${attempts/10}s)`);
                    }
                }

                if (!window.authManager) {
                    throw new Error('Auth manager failed to load');
                }
                this.log('✅ Auth manager found');
            }

            async verifySystems() {
                this.log('🔍 Verifying systems...');
                
                // Check database
                try {
                    const users = await dbManager.getAll('users');
                    this.log(`👥 Found ${users.length} users in database`);
                    
                    if (users.length === 0) {
                        this.log('⚠️ No users found - creating sample users...');
                        await dbManager.insertSampleUsers();
                        const newUsers = await dbManager.getAll('users');
                        this.log(`✅ Created ${newUsers.length} sample users`);
                    }
                    
                    // Verify specific test users
                    const adminUser = users.find(u => u.username === 'admin');
                    const staffUser = users.find(u => u.username === 'staff');
                    
                    if (adminUser) {
                        this.log(`✅ Admin user found: ${adminUser.username} (${adminUser.role})`);
                    } else {
                        this.log('⚠️ Admin user not found');
                    }
                    
                    if (staffUser) {
                        this.log(`✅ Staff user found: ${staffUser.username} (${staffUser.role})`);
                    } else {
                        this.log('⚠️ Staff user not found');
                    }
                    
                } catch (error) {
                    throw new Error(`Database verification failed: ${error.message}`);
                }
                
                this.log('✅ System verification complete');
            }

            setupEventListeners() {
                const loginForm = document.getElementById('login-form');
                loginForm.addEventListener('submit', (e) => this.handleLogin(e));
                
                // Enter key handling
                document.getElementById('username').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        document.getElementById('password').focus();
                    }
                });
                
                document.getElementById('password').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleLogin(e);
                    }
                });
            }

            showLoginForm() {
                document.getElementById('login-form').style.display = 'block';
                document.getElementById('username').focus();
            }

            async handleLogin(event) {
                event.preventDefault();
                
                if (!this.systemReady) {
                    this.showMessage('⚠️ System not ready yet. Please wait.', 'error');
                    return;
                }
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    this.showMessage('❌ Please enter both username and password', 'error');
                    return;
                }

                this.log(`🔐 Attempting login for: ${username}`);\n                this.setLoadingState(true);\n                this.clearMessages();

                try {\n                    this.log('🔍 Starting authentication process...');\n                    const result = await authManager.login(username, password);\n                    \n                    this.log(`🔐 Login result: ${result.success ? 'SUCCESS' : 'FAILED'}`);\n                    if (!result.success) {\n                        this.log(`❌ Login error: ${result.error}`);\n                    }\n\n                    if (result.success) {\n                        this.showMessage(`✅ Welcome ${result.user.full_name}! Redirecting...`, 'success');\n                        this.log(`✅ Login successful - redirecting to dashboard`);\n                        \n                        setTimeout(() => {\n                            window.location.href = '../index.html';\n                        }, 1500);\n                    } else {\n                        this.showMessage(`❌ ${result.error}`, 'error');\n                        this.setLoadingState(false);\n                    }\n                } catch (error) {\n                    this.log(`❌ Login exception: ${error.message}`);\n                    console.error('Login error:', error);\n                    this.showMessage(`❌ Login failed: ${error.message}`, 'error');\n                    this.setLoadingState(false);\n                }\n            }\n\n            setLoadingState(loading) {\n                const loginBtn = document.getElementById('login-btn');\n                const loginText = document.getElementById('login-text');\n                const inputs = document.querySelectorAll('.form-input');\n\n                if (loading) {\n                    loginBtn.disabled = true;\n                    loginText.textContent = 'Logging in...';\n                    inputs.forEach(input => input.disabled = true);\n                } else {\n                    loginBtn.disabled = false;\n                    loginText.textContent = 'Login';\n                    inputs.forEach(input => input.disabled = false);\n                    document.getElementById('username').focus();\n                }\n            }\n\n            updateStatus(status, text) {\n                const indicator = document.getElementById('status-indicator');\n                const statusText = document.getElementById('status-text');\n                \n                indicator.className = `status-indicator status-${status}`;\n                statusText.textContent = text;\n                \n                this.log(`📊 Status: ${status} - ${text}`);\n            }\n\n            showMessage(message, type = 'info') {\n                const container = document.getElementById('message-container');\n                const messageDiv = document.createElement('div');\n                messageDiv.className = `message ${type}-message`;\n                messageDiv.textContent = message;\n                \n                container.innerHTML = '';\n                container.appendChild(messageDiv);\n                \n                // Auto-hide after 5 seconds for non-error messages\n                if (type !== 'error') {\n                    setTimeout(() => {\n                        if (messageDiv.parentNode) {\n                            messageDiv.remove();\n                        }\n                    }, 5000);\n                }\n            }\n\n            clearMessages() {\n                document.getElementById('message-container').innerHTML = '';\n            }\n\n            log(message) {\n                const timestamp = new Date().toLocaleTimeString();\n                const logMessage = `[${timestamp}] ${message}`;\n                console.log(logMessage);\n                \n                if (this.debugMode) {\n                    const debugDiv = document.getElementById('debug-info');\n                    debugDiv.textContent = logMessage;\n                }\n            }\n        }\n\n        // Global functions\n        function toggleDebug() {\n            const loginPage = window.loginPageInstance;\n            if (loginPage) {\n                loginPage.debugMode = !loginPage.debugMode;\n                const debugDiv = document.getElementById('debug-info');\n                debugDiv.style.display = loginPage.debugMode ? 'block' : 'none';\n                loginPage.log(`🐛 Debug mode: ${loginPage.debugMode ? 'ON' : 'OFF'}`);\n            }\n        }\n\n        async function checkSystems() {\n            const loginPage = window.loginPageInstance;\n            if (loginPage) {\n                loginPage.log('🔍 Manual system check requested...');\n                try {\n                    await loginPage.verifySystems();\n                    loginPage.showMessage('✅ System check passed!', 'success');\n                } catch (error) {\n                    loginPage.showMessage(`❌ System check failed: ${error.message}`, 'error');\n                }\n            }\n        }\n\n        function resetSystem() {\n            if (confirm('Reset the system? This will clear all data and reload the page.')) {\n                localStorage.clear();\n                sessionStorage.clear();\n                if (window.dbManager && window.dbManager.clearDatabase) {\n                    window.dbManager.clearDatabase().then(() => {\n                        window.location.reload();\n                    }).catch(() => {\n                        window.location.reload();\n                    });\n                } else {\n                    window.location.reload();\n                }\n            }\n        }\n\n        // Initialize when page loads\n        document.addEventListener('DOMContentLoaded', () => {\n            window.loginPageInstance = new EnhancedLoginPage();\n        });\n    </script>\n</body>\n</html>", "oldText": "            async handleLogin(event) {\n                event.preventDefault();\n                \n                if (!this.systemReady) {\n                    this.showMessage('⚠️ System not ready yet. Please wait.', 'error');\n                    return;\n                }\n                \n                const username = document.getElementById('username').value.trim();\n                const password = document.getElementById('password').value;\n\n                if (!username || !password) {\n                    this.showMessage('❌ Please enter both username and password', 'error');\n                    return;\n                }\n\n                this.log(`🔐 Attempting login for: ${username}`);"}]