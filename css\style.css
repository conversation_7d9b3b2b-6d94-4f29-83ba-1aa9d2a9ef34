/* Optimized CSS for Aanabi Pharmacy Management System v2.0 */

/* === RESET AND BASE STYLES === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    font-size: 14px;
    overflow-x: hidden;
}

/* === LAYOUT VARIABLES === */
:root {
    --header-height: 70px;
    --nav-height: 50px;
    --total-top-offset: 130px; /* Fixed: header + nav + padding */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #34495e;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #f8f9fa;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
    --border-radius: 8px;
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
}

/* === LOADING STATES === */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--light-bg);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* === HEADER STYLES (OPTIMIZED & FIXED) === */
.header {
    background: linear-gradient(135deg, #2c3e50, var(--primary-color));
    color: white;
    padding: 0.75rem 0;
    box-shadow: var(--shadow-light);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: var(--header-height);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo {
    font-size: 1.4rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo::before {
    content: "🏥";
    font-size: 1.6rem;
}

/* === NAVIGATION STYLES (FIXED POSITIONING) === */
.nav {
    background-color: var(--secondary-color);
    padding: 0;
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    z-index: 999;
    box-shadow: var(--shadow-light);
    height: var(--nav-height);
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    height: 100%;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    -ms-overflow-style: none;
    height: 100%;
    align-items: center;
}

.nav-menu::-webkit-scrollbar {
    display: none;
}

.nav-item {
    flex-shrink: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #ecf0f1;
    text-decoration: none;
    transition: var(--transition-medium);
    border-radius: 4px;
    margin-right: 0.25rem;
    font-size: 0.9rem;
    height: var(--nav-height);
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* === MAIN CONTENT (FIXED SPACING) === */
.main-content {
    margin-top: var(--total-top-offset);
    min-height: calc(100vh - 180px);
    padding: 1.5rem 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* === ENHANCED CARD STYLES === */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: var(--transition-fast);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* === ENHANCED FORM STYLES === */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: var(--transition-medium);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-row {
    display: flex;
    gap: 1rem;
    align-items: end;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* === ENHANCED BUTTON STYLES === */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition-medium);
    text-align: center;
    position: relative;
}

.btn:hover:not(.btn-loading) {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.btn-primary {
    background-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
}

.btn-success:hover:not(.btn-loading) {
    background-color: #219a52;
}

.btn-warning {
    background-color: var(--warning-color);
    color: #fff;
}

.btn-warning:hover:not(.btn-loading) {
    background-color: #e67e22;
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover:not(.btn-loading) {
    background-color: #c0392b;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* === ENHANCED TABLE STYLES === */
.table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: 1rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 0.9rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.table th {
    background-color: var(--light-bg);
    font-weight: 600;
    color: #555;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr:hover {
    background-color: var(--light-bg);
}

.table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.table-enhanced {
    font-size: 0.85rem;
}

.table-enhanced th {
    background: linear-gradient(135deg, var(--light-bg), #e9ecef);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

/* === GRID SYSTEM === */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col {
    flex: 1;
    padding: 0 0.5rem;
}

.col-2 { width: 16.66%; }
.col-3 { width: 25%; }
.col-4 { width: 33.33%; }
.col-6 { width: 50%; }
.col-8 { width: 66.66%; }
.col-12 { width: 100%; }

/* === DASHBOARD SPECIFIC STYLES === */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* === ENHANCED PROFIT CARDS === */
.profit-overview {
    margin-bottom: 2rem;
}

.profit-overview h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profit-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.profit-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
    border-left: 4px solid;
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.profit-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.profit-card.bonus {
    border-left-color: var(--success-color);
    background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
}

.profit-card.regular {
    border-left-color: var(--primary-color);
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
}

.profit-card.total {
    border-left-color: var(--warning-color);
    background: linear-gradient(135deg, #ffffff 0%, #fffbf5 100%);
}

.profit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.profit-title {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profit-icon {
    font-size: 1.5rem;
}

.profit-amount {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.profit-subtitle {
    font-size: 0.8rem;
    color: #95a5a6;
    margin-bottom: 1rem;
}

.profit-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
}

.profit-percentage {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.profit-percentage.bonus {
    background: #e8f5e8;
    color: var(--success-color);
}

.profit-percentage.regular {
    background: #e3f2fd;
    color: #1976d2;
}

.profit-percentage.total {
    background: #fff3e0;
    color: #f57c00;
}

.profit-trend {
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* === ENHANCED STOCK CARDS === */
.stock-overview {
    margin-bottom: 2rem;
}

.stock-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stock-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
    border-left: 4px solid;
    text-align: center;
    transition: var(--transition-fast);
}

.stock-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stock-card.bonus-stock {
    border-left-color: var(--success-color);
}

.stock-card.total-stock {
    border-left-color: #8e44ad;
}

.stock-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stock-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

.stock-description {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* === ALERT STYLES === */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: var(--success-color);
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: var(--warning-color);
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: var(--danger-color);
    color: #721c24;
}

.alert-info {
    background-color: #cce7ff;
    border-color: var(--primary-color);
    color: #004085;
}

.alert-mode {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-left-color: #2196f3;
    color: #0d47a1;
    font-weight: 500;
}

/* === SEARCH AND FILTER === */
.search-container {
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: 1rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: var(--transition-medium);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* === MODAL STYLES === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: var(--transition-fast);
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
}

/* === STATUS INDICATORS === */
.status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-paid {
    background-color: #d4edda;
    color: #155724;
}

.status-overdue {
    background-color: #f8d7da;
    color: #721c24;
}

/* === USER INFO & AUTHENTICATION === */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    font-size: 0.9rem;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    opacity: 0.8;
    font-size: 0.8rem;
    text-transform: capitalize;
}

.mode-indicator {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 0.25rem 0.75rem;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 0.5rem;
}

.mode-indicator.admin {
    background: rgba(231, 76, 60, 0.8);
    color: white;
}

.mode-indicator.staff {
    background: rgba(52, 152, 219, 0.8);
    color: white;
}

.mode-switch-btn {
    background: linear-gradient(135deg, var(--success-color), #229954);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-medium);
    margin-top: 1rem;
}

.mode-switch-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.mode-switch-btn.admin-to-staff {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
}

.mode-switch-btn.admin-to-staff:hover {
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
}

/* === ROLE-BASED DISPLAY === */
.admin-only {
    display: none !important;
}

body.admin .admin-only {
    display: block !important;
}

body.admin .admin-only.inline {
    display: inline-block !important;
}

body.admin .admin-only.flex {
    display: flex !important;
}

body.admin .admin-only.grid {
    display: grid !important;
}

.staff-only {
    display: block;
}

body.admin .staff-only {
    display: none !important;
}

/* Role-specific header colors */
body.staff .header {
    background: linear-gradient(135deg, #2c3e50, var(--primary-color));
}

body.admin .header {
    background: linear-gradient(135deg, #8e44ad, var(--danger-color));
}

/* === FOOTER === */
.footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
}

/* === RESPONSIVE DESIGN (OPTIMIZED) === */
@media (max-width: 768px) {
    :root {
        --total-top-offset: 140px; /* Increased for mobile */
    }
    
    .header-content {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
    }

    .nav-menu {
        flex-direction: row;
        gap: 0.25rem;
        padding: 0.25rem 0;
    }

    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .main-content {
        padding: 1rem 0.5rem;
    }

    .row {
        flex-direction: column;
    }

    .col-2, .col-3, .col-4, .col-6, .col-8 {
        width: 100%;
        margin-bottom: 1rem;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .profit-cards {
        grid-template-columns: 1fr;
    }

    .stock-cards {
        grid-template-columns: 1fr;
    }

    .table-container {
        overflow-x: scroll;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .form-row {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 1.2rem;
    }

    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .card-body {
        padding: 1rem;
    }

    .profit-cards {
        gap: 0.5rem;
    }

    .profit-card {
        padding: 1rem;
    }
}

/* === UTILITY CLASSES === */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-muted { color: #7f8c8d; }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }

.hide { display: none !important; }
.show { display: block !important; }

/* === PRINT STYLES === */
@media print {
    .header, .nav, .btn, .modal-overlay, .admin-only, .staff-only, 
    .mode-indicator, .mode-switch-btn {
        display: none !important;
    }

    .main-content {
        margin-top: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
