<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Medicine Management - Aanabi Pharmacy v2.0</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* Enhanced Medicine Management Styles */
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            font-size: 0.9rem;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.8;
            font-size: 0.8rem;
            text-transform: capitalize;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        /* Enhanced Statistics Cards */
        .enhanced-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .enhanced-stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .enhanced-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .enhanced-stat-card.total {
            border-left-color: #3498db;
        }

        .enhanced-stat-card.bonus {
            border-left-color: #27ae60;
        }

        .enhanced-stat-card.value {
            border-left-color: #f39c12;
        }

        .enhanced-stat-card.alerts {
            border-left-color: #e74c3c;
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-icon {
            font-size: 1.5rem;
        }

        .enhanced-stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-subtitle {
            font-size: 0.8rem;
            color: #95a5a6;
        }

        /* Enhanced Table Styles */
        .enhanced-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .enhanced-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 1rem;
            border-bottom: 2px solid #dee2e6;
            font-size: 0.85rem;
        }

        .enhanced-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.85rem;
            vertical-align: middle;
        }

        .enhanced-table tr:hover {
            background: #f8f9fa;
        }

        /* Stock Display Enhancement */
        .stock-display {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .stock-breakdown {
            display: flex;
            gap: 0.5rem;
            font-size: 0.75rem;
        }

        .bonus-stock {
            color: #27ae60;
            font-weight: 500;
        }

        .regular-stock {
            color: #3498db;
            font-weight: 500;
        }

        .total-stock {
            font-weight: bold;
            color: #2c3e50;
        }

        .stock-alert {
            color: #e74c3c;
            font-weight: bold;
        }

        /* Enhanced Modal Styles */
        .modal-xl {
            max-width: 1000px;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #3498db;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Bonus Stock Section */
        .bonus-section {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-left-color: #27ae60;
        }

        .bonus-section .section-title {
            color: #27ae60;
        }

        /* Loyalty Section */
        .loyalty-section {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-left-color: #f39c12;
        }

        .loyalty-section .section-title {
            color: #d35400;
        }

        /* Form Enhancement */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .form-input-enhanced {
            position: relative;
        }

        .input-helper {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            color: #95a5a6;
            font-size: 0.8rem;
            pointer-events: none;
        }

        .calculated-field {
            background: #f8f9fa !important;
            color: #495057;
            font-weight: 500;
        }

        /* Price Calculator */
        .price-calculator {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            border: 1px solid #dee2e6;
        }

        .calculator-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .price-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.5rem;
            font-size: 0.8rem;
        }

        .price-item {
            text-align: center;
            padding: 0.5rem;
            border-radius: 4px;
            background: #f8f9fa;
        }

        .price-value {
            font-weight: bold;
            color: #2c3e50;
        }

        .price-label {
            color: #6c757d;
            margin-top: 0.25rem;
        }

        /* Status Indicators */
        .status-indicator {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .status-discontinued {
            background: #e2e3e5;
            color: #383d41;
        }

        /* Action Buttons Enhancement */
        .action-buttons {
            display: flex;
            gap: 0.25rem;
        }

        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
        }

        /* Search Enhancement */
        .search-filters {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 1rem;
            align-items: end;
        }

        /* Permission-based hiding */
        .admin-only {
            display: none;
        }

        body.admin .admin-only {
            display: block;
        }

        body.admin .admin-only.inline {
            display: inline-block;
        }

        body.admin .admin-only.flex {
            display: flex;
        }

        /* Loading states */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            color: #2c3e50;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .enhanced-stats {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>💊 Aanabi Medicines</h3>
            <p>Loading enhanced medicine management...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                🏥 Aanabi Pharmacy v2.0
                <span style="font-size: 0.7rem; opacity: 0.8; margin-left: 0.5rem;">Enhanced Medicine Management</span>
            </div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
                
                <!-- User Info -->
                <div class="user-info" id="user-info" style="display: none;">
                    <div class="user-avatar" id="user-avatar">A</div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Admin</div>
                        <div class="user-role" id="user-role">Administrator</div>
                    </div>
                    <button class="logout-btn" onclick="handleLogout()">🚪 Logout</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link active">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item admin-only"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item admin-only"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item admin-only"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">💊 Enhanced Medicine Management</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <p class="text-muted">Comprehensive medicine management with bonus stock tracking and loyalty pricing</p>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-primary" onclick="showEnhancedAddMedicineModal()">➕ Add New Medicine</button>
                        <button class="btn btn-success admin-only" onclick="exportEnhancedMedicines()">📤 Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Medicine Statistics with Bonus Stock -->
        <div class="enhanced-stats">
            <div class="enhanced-stat-card total">
                <div class="stat-header">
                    <span class="stat-title">Total Medicines</span>
                    <span class="stat-icon">💊</span>
                </div>
                <div class="enhanced-stat-value" id="total-medicines-count">0</div>
                <div class="stat-subtitle" id="active-medicines-count">0 active medicines</div>
            </div>

            <div class="enhanced-stat-card bonus">
                <div class="stat-header">
                    <span class="stat-title">Bonus Stock Items</span>
                    <span class="stat-icon">🎁</span>
                </div>
                <div class="enhanced-stat-value" id="bonus-stock-items">0</div>
                <div class="stat-subtitle">Medicines with free inventory</div>
            </div>

            <div class="enhanced-stat-card value">
                <div class="stat-header">
                    <span class="stat-title">Total Inventory Value</span>
                    <span class="stat-icon">💰</span>
                </div>
                <div class="enhanced-stat-value" id="total-inventory-value">Rs. 0</div>
                <div class="stat-subtitle">Including bonus stock potential</div>
            </div>

            <div class="enhanced-stat-card alerts">
                <div class="stat-header">
                    <span class="stat-title">Stock Alerts</span>
                    <span class="stat-icon">⚠️</span>
                </div>
                <div class="enhanced-stat-value" id="low-stock-count">0</div>
                <div class="stat-subtitle">Items need attention</div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Search and Filter Section -->
        <div class="search-filters">
            <div class="filter-row">
                <div class="form-group">
                    <label class="form-label">Search Medicines</label>
                    <input type="text" id="medicine-search" class="form-input" 
                           placeholder="Search by name, category, manufacturer, or ID...">
                </div>
                <div class="form-group">
                    <label class="form-label">Category Filter</label>
                    <select id="category-filter" class="form-select" onchange="filterMedicines()">
                        <option value="">All Categories</option>
                        <option value="Pain Relief">Pain Relief</option>
                        <option value="Antibiotic">Antibiotic</option>
                        <option value="Cold & Flu">Cold & Flu</option>
                        <option value="Diabetes">Diabetes</option>
                        <option value="Vitamins">Vitamins</option>
                        <option value="Heart">Heart</option>
                        <option value="Blood Pressure">Blood Pressure</option>
                        <option value="Others">Others</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Stock Status</label>
                    <select id="stock-filter" class="form-select" onchange="filterMedicines()">
                        <option value="">All Stock</option>
                        <option value="in-stock">In Stock</option>
                        <option value="low-stock">Low Stock</option>
                        <option value="out-of-stock">Out of Stock</option>
                        <option value="bonus-available">Has Bonus Stock</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Status Filter</label>
                    <select id="status-filter" class="form-select" onchange="filterMedicines()">
                        <option value="">All Status</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                        <option value="Discontinued">Discontinued</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-secondary" onclick="clearFilters()">🔄 Clear</button>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Medicine List with Bonus Stock Display -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📋 Enhanced Medicine Inventory</h3>
                <small class="text-muted">Complete medicine database with bonus stock tracking and loyalty pricing</small>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Medicine Info</th>
                                <th>Category</th>
                                <th>Stock Breakdown</th>
                                <th>Pricing</th>
                                <th>🏆 Loyalty</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="enhanced-medicines-tbody">
                            <!-- Enhanced medicine data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- ✨ ENHANCED: Add/Edit Medicine Modal with Bonus Stock & Loyalty -->
    <div id="enhanced-medicine-modal" class="modal-overlay" style="display: none;">
        <div class="modal modal-xl">
            <div class="modal-header">
                <h3 class="modal-title" id="enhanced-modal-title">💊 Add New Medicine</h3>
                <button class="modal-close" onclick="closeEnhancedMedicineModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="enhanced-medicine-form">
                    <input type="hidden" id="enhanced-medicine-id" name="medicine_id">
                    
                    <!-- ✨ Basic Information Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <span>📝</span> Basic Medicine Information
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Medicine Name *</label>
                                <input type="text" id="enhanced-medicine-name" name="medicine_name" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Generic Name</label>
                                <input type="text" id="enhanced-generic-name" name="generic_name" class="form-input">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Category *</label>
                                <select id="enhanced-category" name="category" class="form-select" required>
                                    <option value="">Select Category</option>
                                    <option value="Pain Relief">Pain Relief</option>
                                    <option value="Antibiotic">Antibiotic</option>
                                    <option value="Cold & Flu">Cold & Flu</option>
                                    <option value="Diabetes">Diabetes</option>
                                    <option value="Vitamins">Vitamins</option>
                                    <option value="Heart">Heart</option>
                                    <option value="Blood Pressure">Blood Pressure</option>
                                    <option value="Digestive">Digestive</option>
                                    <option value="Skin Care">Skin Care</option>
                                    <option value="Others">Others</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Manufacturer *</label>
                                <input type="text" id="enhanced-manufacturer" name="manufacturer" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Pack Size</label>
                                <input type="text" id="enhanced-pack-size" name="pack_size" class="form-input" placeholder="e.g., 10 tablets">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Unit Type *</label>
                                <select id="enhanced-unit-type" name="unit_type" class="form-select" required>
                                    <option value="">Select Unit</option>
                                    <option value="Tablets">Tablets</option>
                                    <option value="Capsules">Capsules</option>
                                    <option value="Bottle">Bottle</option>
                                    <option value="Vial">Vial</option>
                                    <option value="Tube">Tube</option>
                                    <option value="Box">Box</option>
                                    <option value="Strip">Strip</option>
                                    <option value="Injection">Injection</option>
                                    <option value="Syrup">Syrup</option>
                                    <option value="Drops">Drops</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Enhanced Stock Management Section -->
                    <div class="form-section bonus-section">
                        <h4 class="section-title">
                            <span>📦</span> Enhanced Stock Management
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">📦 Regular Stock (Paid)</label>
                                <input type="number" id="enhanced-regular-stock" name="regular_stock" class="form-input" value="0" min="0">
                                <small class="text-muted">Stock purchased at cost price</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">🎁 Bonus Stock (Free)</label>
                                <input type="number" id="enhanced-bonus-stock" name="bonus_stock" class="form-input" value="0" min="0">
                                <small class="text-muted">Free stock received from suppliers</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">📊 Total Stock (Auto-calculated)</label>
                                <input type="text" id="enhanced-total-stock" class="form-input calculated-field" readonly>
                                <small class="text-muted">Regular + Bonus stock</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">⚠️ Minimum Stock Level *</label>
                                <input type="number" id="enhanced-min-stock-level" name="min_stock_level" class="form-input" required>
                                <small class="text-muted">Alert threshold for reordering</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">📍 Storage Location</label>
                                <input type="text" id="enhanced-storage-location" name="storage_location" class="form-input" placeholder="e.g., A-1, B-2">
                            </div>
                            <div class="form-group">
                                <label class="form-label">💊 Prescription Required</label>
                                <select id="enhanced-prescription-required" name="prescription_required" class="form-select">
                                    <option value="No">No Prescription Required</option>
                                    <option value="Yes">Prescription Required</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Enhanced Pricing Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <span>💰</span> Enhanced Pricing & Margin Calculation
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Purchase Price (Rs.) *</label>
                                <input type="number" id="enhanced-purchase-price" name="purchase_price" class="form-input" 
                                       step="0.01" required onchange="calculateEnhancedMargin()">
                                <small class="text-muted">Cost price for regular stock only</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Selling Price (Rs.) *</label>
                                <input type="number" id="enhanced-selling-price" name="selling_price" class="form-input" 
                                       step="0.01" required onchange="calculateEnhancedMargin()">
                                <small class="text-muted">Retail price for all stock</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">📈 Regular Margin % (Auto-calculated)</label>
                                <input type="text" id="enhanced-margin-percent" name="margin_percent" class="form-input calculated-field" readonly>
                                <small class="text-muted">Margin on regular stock only</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">🎁 Bonus Margin</label>
                                <input type="text" class="form-input calculated-field" value="100%" readonly>
                                <small class="text-muted">Bonus stock always has 100% margin</small>
                            </div>
                        </div>

                        <!-- ✨ Price Calculator Display -->
                        <div class="price-calculator">
                            <div class="calculator-title">💡 Profit Calculator Preview</div>
                            <div class="price-breakdown">
                                <div class="price-item">
                                    <div class="price-value" id="regular-profit-preview">Rs. 0</div>
                                    <div class="price-label">Regular Stock Profit</div>
                                </div>
                                <div class="price-item">
                                    <div class="price-value" id="bonus-profit-preview">Rs. 0</div>
                                    <div class="price-label">Bonus Stock Profit</div>
                                </div>
                                <div class="price-item">
                                    <div class="price-value" id="effective-margin-preview">0%</div>
                                    <div class="price-label">Effective Margin</div>
                                </div>
                                <div class="price-item">
                                    <div class="price-value" id="total-value-preview">Rs. 0</div>
                                    <div class="price-label">Total Stock Value</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Loyalty Points Section -->
                    <div class="form-section loyalty-section">
                        <h4 class="section-title">
                            <span>🏆</span> Loyalty Points Integration
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">🏆 Loyalty Point Price (Optional)</label>
                                <input type="number" id="enhanced-loyalty-point-price" name="loyalty_point_price" 
                                       class="form-input" min="0" placeholder="Points required">
                                <small class="text-muted">Alternative price in loyalty points</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">💰 Points Value Equivalent</label>
                                <input type="text" id="points-value-equivalent" class="form-input calculated-field" readonly>
                                <small class="text-muted">Monetary value at 1 point = Rs. 0.10</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">💡 Points Earning Rate</label>
                                <input type="text" id="points-earning-rate" class="form-input calculated-field" readonly>
                                <small class="text-muted">Points earned per purchase</small>
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Status & Additional Info -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <span>⚙️</span> Status & Additional Information
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">Status *</label>
                                <select id="enhanced-status" name="status" class="form-select" required>
                                    <option value="Active">✅ Active</option>
                                    <option value="Inactive">⏸️ Inactive</option>
                                    <option value="Discontinued">🚫 Discontinued</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">📅 Last Updated</label>
                                <input type="text" class="form-input calculated-field" readonly 
                                       value="Today" id="last-updated-display">
                            </div>
                        </div>
                    </div>

                    <!-- ✨ Form Actions -->
                    <div style="text-align: center; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #dee2e6;">
                        <button type="submit" class="btn btn-primary btn-lg" style="padding: 1rem 2rem;">
                            💊 Save Enhanced Medicine
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeEnhancedMedicineModal()" style="margin-left: 1rem;">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/audit.js"></script>
    <script src="../js/notifications.js"></script>
    <script src="../js/medicines.js"></script>
    
    <script>
        // ✨ Enhanced Medicine Management Initialization
        async function initializeEnhancedMedicines() {
            try {
                console.log('💊 Enhanced medicine management initialization started...');
                
                // Wait for systems to be ready
                await waitForSystems();
                
                // Check authentication
                if (!authManager.isLoggedIn()) {
                    authManager.redirectToLogin('Please login to access medicine management');
                    return;
                }
                
                // Setup user interface
                setupUserInterface();
                
                // Initialize enhanced medicine system
                await initializeEnhancedMedicineSystem();
                
                // Hide loading overlay
                document.getElementById('loading-overlay').style.display = 'none';
                
                console.log('✅ Enhanced medicine management initialized successfully');
                
            } catch (error) {
                console.error('❌ Medicine management initialization failed:', error);
                showError('Failed to initialize medicine management system');
            }
        }

        async function waitForSystems() {
            let attempts = 0;
            const maxAttempts = 100;

            while ((!dbManager?.isReady() || !authManager) && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!dbManager?.isReady() || !authManager) {
                throw new Error('Systems failed to initialize');
            }
        }

        function setupUserInterface() {
            const user = authManager.getCurrentUser();
            if (!user) return;

            // Update user info display
            document.getElementById('user-avatar').textContent = user.full_name.charAt(0).toUpperCase();
            document.getElementById('user-name').textContent = user.full_name;
            document.getElementById('user-role').textContent = user.role;
            document.getElementById('user-info').style.display = 'flex';

            // Set role-based styling
            document.body.className = user.role;

            console.log(`💊 Enhanced medicine management loaded for ${user.full_name} (${user.role})`);
        }

        async function initializeEnhancedMedicineSystem() {
            // Load enhanced statistics
            await loadEnhancedMedicineStatistics();
            
            // Load medicines table
            await loadEnhancedMedicinesTable();
            
            // Setup enhanced event listeners
            setupEnhancedEventListeners();
        }

        async function loadEnhancedMedicineStatistics() {
            try {
                const medicines = await dbManager.getAll('medicines');
                
                const totalMedicines = medicines.length;
                const activeMedicines = medicines.filter(m => m.status === 'Active').length;
                const bonusStockItems = medicines.filter(m => (m.bonus_stock || 0) > 0).length;
                
                let totalInventoryValue = 0;
                let lowStockCount = 0;
                
                medicines.forEach(medicine => {
                    // Calculate inventory value
                    const regularValue = (medicine.regular_stock || 0) * (medicine.purchase_price || 0);
                    const bonusValue = (medicine.bonus_stock || 0) * (medicine.selling_price || 0); // Bonus valued at selling price
                    totalInventoryValue += regularValue + bonusValue;
                    
                    // Check low stock
                    const totalStock = (medicine.regular_stock || 0) + (medicine.bonus_stock || 0);
                    if (totalStock <= (medicine.min_stock_level || 0)) {
                        lowStockCount++;
                    }
                });
                
                // Update enhanced statistics
                document.getElementById('total-medicines-count').textContent = totalMedicines.toLocaleString();
                document.getElementById('active-medicines-count').textContent = `${activeMedicines} active medicines`;
                document.getElementById('bonus-stock-items').textContent = bonusStockItems.toLocaleString();
                document.getElementById('total-inventory-value').textContent = `Rs. ${totalInventoryValue.toLocaleString()}`;
                document.getElementById('low-stock-count').textContent = lowStockCount.toLocaleString();
                
                console.log(`📊 Medicine stats: ${totalMedicines} total, ${bonusStockItems} with bonus, Rs.${totalInventoryValue} value`);
                
            } catch (error) {
                console.error('Error loading enhanced medicine statistics:', error);
            }
        }

        async function loadEnhancedMedicinesTable() {
            try {
                const medicines = await dbManager.getAll('medicines');
                const tbody = document.getElementById('enhanced-medicines-tbody');
                
                if (!tbody) return;
                
                tbody.innerHTML = '';
                
                medicines.forEach(medicine => {
                    const row = document.createElement('tr');
                    
                    // Calculate totals
                    const regularStock = medicine.regular_stock || 0;
                    const bonusStock = medicine.bonus_stock || 0;
                    const totalStock = regularStock + bonusStock;
                    const isLowStock = totalStock <= (medicine.min_stock_level || 0);
                    
                    // Medicine info column
                    const medicineInfo = `
                        <div>
                            <strong>${medicine.medicine_name}</strong><br>
                            <small class="text-muted">${medicine.medicine_id}</small><br>
                            <small>${medicine.generic_name || 'N/A'} | ${medicine.manufacturer}</small>
                        </div>
                    `;
                    
                    // Stock breakdown column
                    const stockBreakdown = `
                        <div class="stock-display">
                            <div class="total-stock ${isLowStock ? 'stock-alert' : ''}">${totalStock} total</div>
                            <div class="stock-breakdown">
                                <span class="regular-stock">📦 ${regularStock}</span>
                                <span class="bonus-stock">🎁 ${bonusStock}</span>
                            </div>
                            <small class="text-muted">Min: ${medicine.min_stock_level || 0}</small>
                        </div>
                    `;
                    
                    // Pricing column
                    const margin = medicine.purchase_price && medicine.selling_price ? 
                        Math.round(((medicine.selling_price - medicine.purchase_price) / medicine.purchase_price) * 100) : 0;
                    
                    const pricingInfo = `
                        <div>
                            <div><strong>Rs. ${(medicine.selling_price || 0).toLocaleString()}</strong></div>
                            <small class="text-muted">Cost: Rs. ${(medicine.purchase_price || 0).toLocaleString()}</small><br>
                            <small class="text-muted">Margin: ${margin}%</small>
                        </div>
                    `;
                    
                    // Loyalty points column
                    const loyaltyInfo = medicine.loyalty_point_price ? 
                        `<div>${medicine.loyalty_point_price} pts<br><small class="text-muted">Rs. ${(medicine.loyalty_point_price * 0.10).toFixed(2)} value</small></div>` :
                        '<small class="text-muted">Not available</small>';
                    
                    // Status column
                    const statusClass = `status-${medicine.status.toLowerCase()}`;
                    const statusBadge = `<span class="status-indicator ${statusClass}">${medicine.status}</span>`;
                    
                    // Actions column
                    const actions = `
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-primary btn-icon" onclick="editEnhancedMedicine('${medicine.medicine_id}')" title="Edit">✏️</button>
                            <button class="btn btn-sm btn-info btn-icon" onclick="viewMedicineDetails('${medicine.medicine_id}')" title="View Details">👁️</button>
                            <button class="btn btn-sm btn-success btn-icon" onclick="adjustStock('${medicine.medicine_id}')" title="Adjust Stock">📦</button>
                            <button class="btn btn-sm btn-danger btn-icon admin-only" onclick="deleteMedicine('${medicine.medicine_id}')" title="Delete">🗑️</button>
                        </div>
                    `;
                    
                    row.innerHTML = `
                        <td>${medicineInfo}</td>
                        <td><span class="badge badge-info">${medicine.category}</span></td>
                        <td>${stockBreakdown}</td>
                        <td>${pricingInfo}</td>
                        <td>${loyaltyInfo}</td>
                        <td>${statusBadge}</td>
                        <td>${actions}</td>
                    `;
                    
                    tbody.appendChild(row);
                });
                
            } catch (error) {
                console.error('Error loading enhanced medicines table:', error);
            }
        }

        function setupEnhancedEventListeners() {
            // Enhanced medicine form
            const medicineForm = document.getElementById('enhanced-medicine-form');
            if (medicineForm) {
                medicineForm.addEventListener('submit', handleEnhancedMedicineSubmit);
            }
            
            // Search functionality
            const searchInput = document.getElementById('medicine-search');
            if (searchInput) {
                searchInput.addEventListener('input', handleMedicineSearch);
            }
            
            // Real-time calculation listeners
            const stockInputs = ['enhanced-regular-stock', 'enhanced-bonus-stock'];
            stockInputs.forEach(id => {
                const input = document.getElementById(id);
                if (input) {
                    input.addEventListener('input', calculateTotalStock);
                }
            });
            
            const loyaltyInput = document.getElementById('enhanced-loyalty-point-price');
            if (loyaltyInput) {
                loyaltyInput.addEventListener('input', calculateLoyaltyEquivalent);
            }
        }

        // Enhanced calculation functions
        function calculateEnhancedMargin() {
            const purchasePrice = parseFloat(document.getElementById('enhanced-purchase-price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('enhanced-selling-price').value) || 0;
            
            if (purchasePrice > 0 && sellingPrice > 0) {
                const margin = ((sellingPrice - purchasePrice) / purchasePrice) * 100;
                document.getElementById('enhanced-margin-percent').value = margin.toFixed(2) + '%';
                
                // Update profit calculator
                updateProfitCalculator();
            }
        }

        function calculateTotalStock() {
            const regularStock = parseInt(document.getElementById('enhanced-regular-stock').value) || 0;
            const bonusStock = parseInt(document.getElementById('enhanced-bonus-stock').value) || 0;
            const totalStock = regularStock + bonusStock;
            
            document.getElementById('enhanced-total-stock').value = totalStock;
            
            // Update profit calculator
            updateProfitCalculator();
        }

        function calculateLoyaltyEquivalent() {
            const loyaltyPoints = parseInt(document.getElementById('enhanced-loyalty-point-price').value) || 0;
            const pointValue = loyaltyPoints * 0.10; // 1 point = Rs. 0.10
            
            document.getElementById('points-value-equivalent').value = loyaltyPoints > 0 ? `Rs. ${pointValue.toFixed(2)}` : '';
            
            // Calculate earning rate
            const sellingPrice = parseFloat(document.getElementById('enhanced-selling-price').value) || 0;
            if (sellingPrice > 0) {
                const earningRate = Math.floor(sellingPrice / 20); // 1 point per Rs. 20
                document.getElementById('points-earning-rate').value = `${earningRate} points earned`;
            }
        }

        function updateProfitCalculator() {
            const regularStock = parseInt(document.getElementById('enhanced-regular-stock').value) || 0;
            const bonusStock = parseInt(document.getElementById('enhanced-bonus-stock').value) || 0;
            const purchasePrice = parseFloat(document.getElementById('enhanced-purchase-price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('enhanced-selling-price').value) || 0;
            
            if (sellingPrice > 0) {
                // Calculate profits
                const regularProfit = regularStock * (sellingPrice - purchasePrice);
                const bonusProfit = bonusStock * sellingPrice; // 100% margin on bonus
                const totalValue = (regularStock * purchasePrice) + (bonusStock * sellingPrice);
                
                // Calculate effective margin
                const totalRevenue = (regularStock + bonusStock) * sellingPrice;
                const totalCost = regularStock * purchasePrice; // Bonus has no cost
                const effectiveMargin = totalRevenue > 0 ? ((totalRevenue - totalCost) / totalRevenue) * 100 : 0;
                
                // Update display
                document.getElementById('regular-profit-preview').textContent = `Rs. ${regularProfit.toLocaleString()}`;
                document.getElementById('bonus-profit-preview').textContent = `Rs. ${bonusProfit.toLocaleString()}`;
                document.getElementById('effective-margin-preview').textContent = `${effectiveMargin.toFixed(1)}%`;
                document.getElementById('total-value-preview').textContent = `Rs. ${totalValue.toLocaleString()}`;
            }
        }

        // Modal functions
        function showEnhancedAddMedicineModal() {
            document.getElementById('enhanced-medicine-modal').style.display = 'flex';
            document.getElementById('enhanced-modal-title').textContent = '💊 Add New Medicine';
            document.getElementById('enhanced-medicine-form').reset();
            
            // Set defaults
            document.getElementById('enhanced-regular-stock').value = '0';
            document.getElementById('enhanced-bonus-stock').value = '0';
            document.getElementById('enhanced-total-stock').value = '0';
            document.getElementById('last-updated-display').value = 'Today';
        }

        function closeEnhancedMedicineModal() {
            document.getElementById('enhanced-medicine-modal').style.display = 'none';
        }

        // Placeholder functions for enhanced features
        async function handleEnhancedMedicineSubmit(event) {
            event.preventDefault();
            console.log('💊 Enhanced medicine form submitted');
            showAlert('Enhanced medicine save functionality ready for implementation! 💊', 'info');
        }

        function handleMedicineSearch(event) {
            const searchTerm = event.target.value.toLowerCase();
            console.log('🔍 Searching medicines:', searchTerm);
            // Search functionality will be implemented
        }

        function filterMedicines() {
            console.log('🔽 Filtering medicines');
            // Filter functionality will be implemented
        }

        function clearFilters() {
            document.getElementById('category-filter').value = '';
            document.getElementById('stock-filter').value = '';
            document.getElementById('status-filter').value = '';
            document.getElementById('medicine-search').value = '';
            console.log('🔄 Filters cleared');
        }

        function editEnhancedMedicine(medicineId) {
            console.log('✏️ Edit medicine:', medicineId);
            showAlert(`Enhanced edit functionality for ${medicineId} ready for implementation! ✏️`, 'info');
        }

        function viewMedicineDetails(medicineId) {
            console.log('👁️ View medicine details:', medicineId);
            showAlert(`Enhanced medicine details view for ${medicineId} ready for implementation! 👁️`, 'info');
        }

        function adjustStock(medicineId) {
            console.log('📦 Adjust stock:', medicineId);
            showAlert(`Enhanced stock adjustment for ${medicineId} ready for implementation! 📦`, 'info');
        }

        function deleteMedicine(medicineId) {
            if (confirm('Are you sure you want to delete this medicine?')) {
                console.log('🗑️ Delete medicine:', medicineId);
                showAlert(`Enhanced delete functionality for ${medicineId} ready for implementation! 🗑️`, 'info');
            }
        }

        function exportEnhancedMedicines() {
            console.log('📤 Export enhanced medicines');
            showAlert('Enhanced medicine export with bonus stock data ready for implementation! 📤', 'info');
        }

        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                authManager.logout();
                window.location.href = 'login.html';
            }
        }

        function showError(message) {
            showAlert(message, 'error');
        }

        function showAlert(message, type) {
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(alert, mainContent.firstChild);

            setTimeout(() => alert.remove(), 5000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeEnhancedMedicines);
    </script>
</body>
</html>