<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Aanabi Pharmacy Management System v2.0</title>
    <link rel="stylesheet" href="css/style.css">
    <!-- Preload critical resources for better performance -->
    <link rel="preload" href="js/utils.js" as="script">
    <link rel="preload" href="js/database.js" as="script">
    <link rel="preload" href="js/auth.js" as="script">
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                Aanabi Pharmacy v2.0
                <span style="font-size: 0.7rem; opacity: 0.8; margin-left: 0.5rem;">Simplified Edition</span>
            </div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
                
                <!-- User Info -->
                <div class="user-info" id="user-info">
                    <div class="user-avatar" id="user-avatar">S</div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Staff Member</div>
                        <div class="user-role" id="user-role">Staff</div>
                    </div>
                    <span class="mode-indicator staff" id="mode-indicator">Staff Mode</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="index.html" class="nav-link active">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="pages/medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="pages/inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="pages/sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item admin-only"><a href="pages/purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="pages/customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item admin-only"><a href="pages/suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="pages/reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item admin-only"><a href="pages/settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Mode Info Banner -->
        <div class="alert alert-mode">
            <strong>📍 Current Mode: <span id="current-mode-text">Staff</span></strong> | 
            <span id="mode-description">Limited access mode. Go to Settings to switch to Admin mode for full access.</span>
        </div>

        <!-- Enhanced Profit Overview -->
        <div class="profit-overview">
            <h2>💰 Today's Profit Analysis</h2>
            <div class="profit-cards">
                <!-- Bonus Profit Card -->
                <div class="profit-card bonus">
                    <div class="profit-header">
                        <span class="profit-title">Bonus Profit</span>
                        <span class="profit-icon">🎁</span>
                    </div>
                    <div class="profit-amount" id="bonus-profit-amount">Rs. 0</div>
                    <div class="profit-subtitle">From free inventory (100% margin)</div>
                    <div class="profit-details">
                        <span class="profit-percentage bonus" id="bonus-profit-percentage">0%</span>
                        <span class="profit-trend" id="bonus-profit-trend">of total profit</span>
                    </div>
                </div>

                <!-- Regular Profit Card -->
                <div class="profit-card regular">
                    <div class="profit-header">
                        <span class="profit-title">Regular Profit</span>
                        <span class="profit-icon">📦</span>
                    </div>
                    <div class="profit-amount" id="regular-profit-amount">Rs. 0</div>
                    <div class="profit-subtitle">From paid inventory</div>
                    <div class="profit-details">
                        <span class="profit-percentage regular" id="regular-profit-percentage">0%</span>
                        <span class="profit-trend" id="regular-profit-trend">of total profit</span>
                    </div>
                </div>

                <!-- Total Profit Card -->
                <div class="profit-card total">
                    <div class="profit-header">
                        <span class="profit-title">Total Profit</span>
                        <span class="profit-icon">📈</span>
                    </div>
                    <div class="profit-amount" id="total-profit-amount">Rs. 0</div>
                    <div class="profit-subtitle">Combined daily earnings</div>
                    <div class="profit-details">
                        <span class="profit-percentage total" id="total-profit-margin">0%</span>
                        <span class="profit-trend">effective margin</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Stock Overview -->
        <div class="stock-overview">
            <h2>📊 Inventory Overview</h2>
            <div class="stock-cards">
                <div class="stock-card bonus-stock">
                    <div class="stock-value" id="total-bonus-stock">0</div>
                    <div class="stock-label">Total Bonus Stock</div>
                    <div class="stock-description">Free inventory items</div>
                </div>
                
                <div class="stock-card total-stock">
                    <div class="stock-value" id="total-inventory-value">Rs. 0</div>
                    <div class="stock-label">Total Inventory Value</div>
                    <div class="stock-description">Estimated stock worth</div>
                </div>
                
                <div class="stock-card">
                    <div class="stock-value" id="loyalty-points-outstanding">0</div>
                    <div class="stock-label">Outstanding Loyalty Points</div>
                    <div class="stock-description">Points across all customers</div>
                </div>
            </div>
        </div>

        <!-- Traditional Dashboard Stats -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <span class="stat-icon">💊</span>
                <div class="stat-value" id="total-medicines">0</div>
                <div class="stat-label">Total Medicines</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">📦</span>
                <div class="stat-value" id="low-stock-items">0</div>
                <div class="stat-label">Low Stock Items</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">⚠️</span>
                <div class="stat-value" id="expiring-items">0</div>
                <div class="stat-label">Items Expiring Soon</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💳</span>
                <div class="stat-value" id="today-sales-count">0</div>
                <div class="stat-label">Today's Transactions</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-value" id="total-customers">0</div>
                <div class="stat-label">Total Customers</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">🏭</span>
                <div class="stat-value" id="total-suppliers">0</div>
                <div class="stat-label">Total Suppliers</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🚀 Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <a href="pages/sales.html" class="btn btn-primary btn-lg">💳 New Sale</a>
                            </div>
                            <div class="col-6">
                                <a href="pages/medicines.html" class="btn btn-success btn-lg">💊 Add Medicine</a>
                            </div>
                            <div class="col-6">
                                <a href="pages/customers.html" class="btn btn-warning btn-lg">👥 Add Customer</a>
                            </div>
                            <div class="col-6 admin-only">
                                <a href="pages/purchase.html" class="btn btn-danger btn-lg">🛒 New Purchase</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🔔 Smart Alerts</h3>
                    </div>
                    <div class="card-body">
                        <div id="alerts-container">
                            <div class="alert alert-info">
                                <strong>🔄 System Loading...</strong><br>
                                Initializing enhanced pharmacy management system...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Recent Transactions -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">📋 Recent Sales Transactions</h3>
                <small class="text-muted">Latest sales with profit breakdown</small>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table table-enhanced">
                        <thead>
                            <tr>
                                <th>Sale ID</th>
                                <th>Date & Time</th>
                                <th>Customer</th>
                                <th>Medicine</th>
                                <th>Qty (B+R)</th>
                                <th>Total Amount</th>
                                <th>Profit</th>
                                <th>Payment</th>
                            </tr>
                        </thead>
                        <tbody id="recent-sales-tbody">
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    <div class="loading-spinner" style="margin: 1rem auto;"></div>
                                    Loading recent transactions...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Enhanced Stock Alerts -->
        <div class="row">
            <div class="col-6">
                <div class="card" id="low-stock-section">
                    <div class="card-header">
                        <h3 class="card-title">📦 Low Stock Alert</h3>
                        <small class="text-muted">Items requiring immediate attention</small>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table table-enhanced">
                                <thead>
                                    <tr>
                                        <th>Medicine</th>
                                        <th>Stock (R+B)</th>
                                        <th>Min Level</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="low-stock-tbody">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <div class="loading-spinner" style="margin: 1rem auto;"></div>
                                            Checking stock levels...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="pages/medicines.html" class="btn btn-primary">📊 View All Stock</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card" id="expiring-items-section">
                    <div class="card-header">
                        <h3 class="card-title">⏰ Expiry Alerts</h3>
                        <small class="text-muted">Items expiring within alert threshold</small>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table table-enhanced">
                                <thead>
                                    <tr>
                                        <th>Medicine</th>
                                        <th>Batch</th>
                                        <th>Expiry Date</th>
                                        <th>Days Left</th>
                                    </tr>
                                </thead>
                                <tbody id="expiring-items-tbody">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <div class="loading-spinner" style="margin: 1rem auto;"></div>
                                            Checking expiry dates...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="pages/inventory.html" class="btn btn-warning">📅 View All Expiry</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div>&copy; 2025 Aanabi Pharmacy Management System v2.0 - Simplified Edition</div>
    </footer>

    <!-- Optimized Script Loading with Performance Monitoring -->
    <script>
        // Performance monitoring
        const startTime = performance.now();
        
        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.innerHTML = `
                    <div style="text-align: center; color: #e74c3c;">
                        <h3>⚠️ System Error</h3>
                        <p>Failed to initialize pharmacy system.</p>
                        <button onclick="location.reload()" class="btn btn-primary" style="margin-top: 1rem;">🔄 Reload Page</button>
                    </div>
                `;
            }
        });
        
        // Performance logging
        window.addEventListener('load', () => {
            const loadTime = performance.now() - startTime;
            console.log(`🚀 Total page load time: ${loadTime.toFixed(2)}ms`);
        });
    </script>

    <!-- Load scripts in optimized order -->
    <script src="js/utils.js"></script>
    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/audit.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>