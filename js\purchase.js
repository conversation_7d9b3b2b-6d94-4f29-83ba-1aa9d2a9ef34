// Purchase management functionality

class PurchaseManager {
    constructor() {
        this.purchases = [];
        this.suppliers = [];
        this.medicines = [];
        this.filteredPurchases = [];
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadData() {
        try {
            // Wait for database to be ready
            if (!dbManager.isReady()) {
                setTimeout(() => this.loadData(), 1000);
                return;
            }

            this.purchases = await dbManager.getAll('purchases');
            this.suppliers = await dbManager.getAll('suppliers');
            this.medicines = await dbManager.getAll('medicines');

            this.populateDropdowns();
            this.filteredPurchases = [...this.purchases];
            this.renderPurchases();
            this.updateStatistics();
            this.setTodayAsDefault();
        } catch (error) {
            console.error('Error loading purchase data:', error);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('purchase-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterPurchases(e.target.value));
        }

        // Form submission
        const form = document.getElementById('purchase-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    populateDropdowns() {
        // Populate supplier dropdown
        const supplierSelect = document.getElementById('supplier-select');
        if (supplierSelect) {
            supplierSelect.innerHTML = '<option value="">Select Supplier</option>';
            this.suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.supplier_name;
                option.textContent = supplier.supplier_name;
                supplierSelect.appendChild(option);
            });
        }

        // Populate medicine dropdown
        const medicineSelect = document.getElementById('medicine-select');
        if (medicineSelect) {
            medicineSelect.innerHTML = '<option value="">Select Medicine</option>';
            this.medicines.forEach(medicine => {
                const option = document.createElement('option');
                option.value = medicine.medicine_id;
                option.textContent = medicine.medicine_name;
                medicineSelect.appendChild(option);
            });
        }
    }

    setTodayAsDefault() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('payment-due-date').value = today;
    }

    filterPurchases(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredPurchases = this.purchases.filter(purchase => 
            purchase.purchase_id.toLowerCase().includes(term) ||
            purchase.supplier_name.toLowerCase().includes(term) ||
            purchase.medicine_name.toLowerCase().includes(term) ||
            (purchase.invoice_number && purchase.invoice_number.toLowerCase().includes(term))
        );
        this.renderPurchases();
    }

    renderPurchases() {
        const tbody = document.getElementById('purchases-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredPurchases.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" class="text-center text-muted">No purchases found</td></tr>';
            return;
        }

        // Sort purchases by date (newest first)
        const sortedPurchases = [...this.filteredPurchases].sort((a, b) => new Date(b.date) - new Date(a.date));

        sortedPurchases.forEach(purchase => {
            const row = document.createElement('tr');
            
            // Highlight overdue payments
            const dueDate = new Date(purchase.payment_due_date);
            const today = new Date();
            if (purchase.payment_status === 'Pending' && dueDate < today) {
                row.style.backgroundColor = '#ffebee';
            }

            row.innerHTML = `
                <td>${purchase.purchase_id}</td>
                <td>${new Date(purchase.date).toLocaleDateString()}</td>
                <td>${purchase.supplier_name}</td>
                <td>${purchase.invoice_number || '-'}</td>
                <td>${purchase.medicine_name}</td>
                <td>${purchase.quantity_ordered}${purchase.bonus_items > 0 ? ` (+${purchase.bonus_items} bonus)` : ''}</td>
                <td>Rs. ${parseFloat(purchase.unit_cost).toFixed(2)}</td>
                <td>Rs. ${parseFloat(purchase.total_amount).toFixed(2)}</td>
                <td>${purchase.payment_terms}</td>
                <td>${purchase.payment_due_date ? new Date(purchase.payment_due_date).toLocaleDateString() : '-'}</td>
                <td><span class="status status-${purchase.payment_status.toLowerCase()}">${purchase.payment_status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editPurchase('${purchase.purchase_id}')">Edit</button>
                    <button class="btn btn-sm btn-success" onclick="markAsPaid('${purchase.purchase_id}')">Mark Paid</button>
                    <button class="btn btn-sm btn-danger" onclick="deletePurchase('${purchase.purchase_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStatistics() {
        const totalPurchases = this.purchases.length;
        const totalAmount = this.purchases.reduce((sum, p) => sum + (parseFloat(p.total_amount) || 0), 0);
        const pendingPayments = this.purchases.filter(p => p.payment_status === 'Pending').length;
        
        // This month's purchases
        const thisMonth = new Date();
        const firstDayOfMonth = new Date(thisMonth.getFullYear(), thisMonth.getMonth(), 1);
        const thisMonthPurchases = this.purchases.filter(p => new Date(p.date) >= firstDayOfMonth);
        const thisMonthAmount = thisMonthPurchases.reduce((sum, p) => sum + (parseFloat(p.total_amount) || 0), 0);

        document.getElementById('total-purchases-count').textContent = totalPurchases;
        document.getElementById('total-purchase-amount').textContent = `Rs. ${totalAmount.toLocaleString()}`;
        document.getElementById('pending-payments').textContent = pendingPayments;
        document.getElementById('this-month-purchases').textContent = `Rs. ${thisMonthAmount.toLocaleString()}`;
    }

    async handleFormSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const purchaseData = Object.fromEntries(formData.entries());
        
        // Get medicine name
        const medicine = this.medicines.find(m => m.medicine_id === purchaseData.medicine_id);
        if (!medicine) {
            this.showAlert('Please select a valid medicine', 'danger');
            return;
        }

        purchaseData.medicine_name = medicine.medicine_name;
        purchaseData.quantity_ordered = parseInt(purchaseData.quantity_ordered);
        purchaseData.unit_cost = parseFloat(purchaseData.unit_cost);
        purchaseData.total_amount = parseFloat(purchaseData.total_amount);
        purchaseData.bonus_items = parseInt(purchaseData.bonus_items) || 0;

        // Set date if not editing
        if (!purchaseData.purchase_id) {
            purchaseData.date = new Date().toISOString().split('T')[0];
        }

        try {
            if (purchaseData.purchase_id) {
                // Update existing purchase
                await dbManager.update('purchases', purchaseData);
                this.showAlert('Purchase updated successfully!', 'success');
            } else {
                // Add new purchase
                purchaseData.purchase_id = await dbManager.generateId('purchases', 'PUR');
                await dbManager.insert('purchases', purchaseData);

                // ✨ ENHANCED: Update medicine stock using enhanced schema
                await this.updateMedicineStockEnhanced(medicine, purchaseData);

                // Add to inventory if batch info provided
                if (purchaseData.batch_number && purchaseData.expiry_date) {
                    await this.createInventoryRecord(purchaseData, medicine);
                }

                // ✨ NEW: Update bonus tracking
                if (purchaseData.bonus_items > 0) {
                    medicine.total_bonus_received = (medicine.total_bonus_received || 0) + purchaseData.bonus_items;
                    medicine.last_bonus_date = purchaseData.date;
                    await dbManager.update('medicines', medicine);
                }

                this.showAlert('Purchase order created successfully!', 'success');
            }
            
            this.closePurchaseModal();
            await this.loadData();
        } catch (error) {
            console.error('Error saving purchase:', error);
            this.showAlert('Error saving purchase. Please try again.', 'danger');
        }
    }
    
    // ✨ NEW: Enhanced stock update method
    async updateMedicineStockEnhanced(medicine, purchaseData) {
        try {
            // Split stock into regular and bonus buckets
            const regularQuantity = purchaseData.quantity_ordered;
            const bonusQuantity = purchaseData.bonus_items;
            
            // Update regular stock (paid inventory)
            medicine.regular_stock = (medicine.regular_stock || 0) + regularQuantity;
            
            // Update bonus stock (free inventory)
            medicine.bonus_stock = (medicine.bonus_stock || 0) + bonusQuantity;
            
            // Update total current stock for compatibility
            medicine.current_stock = medicine.regular_stock + medicine.bonus_stock;
            
            // Update last purchase info
            medicine.last_purchase_date = purchaseData.date;
            medicine.last_purchase_cost = purchaseData.unit_cost;
            
            await dbManager.update('medicines', medicine);
            
            console.log(`📦 Stock updated: ${medicine.medicine_name} - Regular: +${regularQuantity}, Bonus: +${bonusQuantity}`);
            
            // ✨ NEW: Create notification for significant stock increase
            if ((regularQuantity + bonusQuantity) >= 100) {
                await notificationManager.createNotification({
                    type: 'STOCK_UPDATE',
                    title: 'Large Stock Addition',
                    message: `${medicine.medicine_name}: Added ${regularQuantity + bonusQuantity} units (${bonusQuantity} bonus)`,
                    priority: 'Low',
                    related_record_id: medicine.medicine_id,
                    related_module: 'medicines'
                });
            }
            
        } catch (error) {
            console.error('Error updating enhanced medicine stock:', error);
            throw error;
        }
    }
    
    // ✨ NEW: Enhanced inventory record creation
    async createInventoryRecord(purchaseData, medicine) {
        try {
            const totalQuantity = purchaseData.quantity_ordered + purchaseData.bonus_items;
            
            const inventoryData = {
                inventory_id: await dbManager.generateId('inventory', 'INV'),
                medicine_id: purchaseData.medicine_id,
                medicine_name: purchaseData.medicine_name,
                batch_number: purchaseData.batch_number,
                expiry_date: purchaseData.expiry_date,
                current_stock: totalQuantity,
                regular_stock: purchaseData.quantity_ordered, // ✨ NEW: Track regular vs bonus in inventory
                bonus_stock: purchaseData.bonus_items,        // ✨ NEW: Track bonus stock in inventory
                manufacturing_date: purchaseData.manufacturing_date || new Date().toISOString().split('T')[0],
                supplier_name: purchaseData.supplier_name,
                purchase_date: purchaseData.date,
                unit_cost: purchaseData.unit_cost,
                status: 'Active'
            };
            
            await dbManager.insert('inventory', inventoryData);
            console.log(`📋 Inventory record created: ${inventoryData.inventory_id}`);
            
        } catch (error) {
            console.error('Error creating inventory record:', error);
            throw error;
        }
    }

    showAlert(message, type) {
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        setTimeout(() => alert.remove(), 3000);
    }

    closePurchaseModal() {
        document.getElementById('purchase-modal').style.display = 'none';
        document.getElementById('purchase-form').reset();
        document.getElementById('purchase-id').value = '';
        document.getElementById('purchase-modal-title').textContent = 'New Purchase Order';
        this.setTodayAsDefault();
    }
}

// Global functions
function showAddPurchaseModal() {
    document.getElementById('purchase-modal').style.display = 'flex';
    document.getElementById('purchase-form').reset();
    document.getElementById('purchase-id').value = '';
    document.getElementById('purchase-modal-title').textContent = 'New Purchase Order';
    purchaseManager.setTodayAsDefault();
}

function closePurchaseModal() {
    purchaseManager.closePurchaseModal();
}

function loadSupplierInfo() {
    const supplierName = document.getElementById('supplier-select').value;
    const supplier = purchaseManager.suppliers.find(s => s.supplier_name === supplierName);
    
    if (supplier) {
        document.getElementById('payment-terms').value = supplier.payment_terms;
        calculateDueDate();
    }
}

function loadMedicineInfo() {
    const medicineId = document.getElementById('medicine-select').value;
    const medicine = purchaseManager.medicines.find(m => m.medicine_id === medicineId);
    
    if (medicine) {
        document.getElementById('unit-cost').value = medicine.purchase_price || '';
        calculateTotal();
    }
}

function calculateDueDate() {
    const paymentTerms = document.getElementById('payment-terms').value;
    const today = new Date();
    let dueDate = new Date(today);

    switch (paymentTerms) {
        case 'Net 15':
            dueDate.setDate(today.getDate() + 15);
            break;
        case 'Net 30':
            dueDate.setDate(today.getDate() + 30);
            break;
        case 'Net 45':
            dueDate.setDate(today.getDate() + 45);
            break;
        case 'Net 60':
            dueDate.setDate(today.getDate() + 60);
            break;
        default: // Cash
            dueDate = today;
    }

    document.getElementById('payment-due-date').value = dueDate.toISOString().split('T')[0];
}

function calculateTotal() {
    const quantity = parseFloat(document.getElementById('quantity-ordered').value) || 0;
    const unitCost = parseFloat(document.getElementById('unit-cost').value) || 0;
    const total = quantity * unitCost;
    
    document.getElementById('total-amount').value = `Rs. ${total.toFixed(2)}`;
}

async function editPurchase(purchaseId) {
    try {
        const purchase = await dbManager.get('purchases', purchaseId);
        if (!purchase) {
            alert('Purchase not found');
            return;
        }

        // Populate form with purchase data
        document.getElementById('purchase-id').value = purchase.purchase_id;
        document.getElementById('supplier-select').value = purchase.supplier_name;
        document.getElementById('invoice-number').value = purchase.invoice_number || '';
        document.getElementById('payment-terms').value = purchase.payment_terms;
        document.getElementById('payment-due-date').value = purchase.payment_due_date;
        document.getElementById('medicine-select').value = purchase.medicine_id;
        document.getElementById('quantity-ordered').value = purchase.quantity_ordered;
        document.getElementById('unit-cost').value = purchase.unit_cost;
        document.getElementById('bonus-items').value = purchase.bonus_items || 0;
        document.getElementById('batch-number').value = purchase.batch_number || '';
        document.getElementById('expiry-date').value = purchase.expiry_date || '';
        document.getElementById('payment-status').value = purchase.payment_status;
        document.getElementById('total-amount').value = `Rs. ${parseFloat(purchase.total_amount).toFixed(2)}`;

        // Show modal
        document.getElementById('purchase-modal').style.display = 'flex';
        document.getElementById('purchase-modal-title').textContent = 'Edit Purchase Order';
    } catch (error) {
        console.error('Error loading purchase for edit:', error);
        alert('Error loading purchase data');
    }
}

async function markAsPaid(purchaseId) {
    if (!confirm('Mark this purchase as paid?')) {
        return;
    }

    try {
        const purchase = await dbManager.get('purchases', purchaseId);
        if (purchase) {
            purchase.payment_status = 'Paid';
            await dbManager.update('purchases', purchase);
            purchaseManager.showAlert('Purchase marked as paid!', 'success');
            await purchaseManager.loadData();
        }
    } catch (error) {
        console.error('Error updating payment status:', error);
        purchaseManager.showAlert('Error updating payment status. Please try again.', 'danger');
    }
}

async function deletePurchase(purchaseId) {
    if (!confirm('Are you sure you want to delete this purchase? This action cannot be undone.')) {
        return;
    }

    try {
        // Get purchase to restore stock
        const purchase = await dbManager.get('purchases', purchaseId);
        if (purchase) {
            // Restore medicine stock
            const medicine = await dbManager.get('medicines', purchase.medicine_id);
            if (medicine) {
                const totalQuantity = purchase.quantity_ordered + (purchase.bonus_items || 0);
                medicine.current_stock = Math.max(0, medicine.current_stock - totalQuantity);
                await dbManager.update('medicines', medicine);
            }
        }

        await dbManager.delete('purchases', purchaseId);
        purchaseManager.showAlert('Purchase deleted successfully!', 'success');
        await purchaseManager.loadData();
    } catch (error) {
        console.error('Error deleting purchase:', error);
        purchaseManager.showAlert('Error deleting purchase. Please try again.', 'danger');
    }
}

function exportPurchases() {
    try {
        const headers = [
            'Purchase ID', 'Date', 'Supplier Name', 'Invoice Number', 'Medicine Name',
            'Quantity Ordered', 'Bonus Items', 'Unit Cost', 'Total Amount',
            'Payment Terms', 'Payment Due Date', 'Payment Status', 'Batch Number', 'Expiry Date'
        ];
        
        let csvContent = headers.join(',') + '\n';
        
        purchaseManager.purchases.forEach(purchase => {
            const row = [
                purchase.purchase_id,
                purchase.date,
                `"${purchase.supplier_name}"`,
                purchase.invoice_number || '',
                `"${purchase.medicine_name}"`,
                purchase.quantity_ordered,
                purchase.bonus_items || 0,
                purchase.unit_cost,
                purchase.total_amount,
                purchase.payment_terms,
                purchase.payment_due_date || '',
                purchase.payment_status,
                purchase.batch_number || '',
                purchase.expiry_date || ''
            ];
            csvContent += row.join(',') + '\n';
        });
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `purchases_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        purchaseManager.showAlert('Purchases exported successfully!', 'success');
    } catch (error) {
        console.error('Error exporting purchases:', error);
        purchaseManager.showAlert('Error exporting purchases. Please try again.', 'danger');
    }
}

// Initialize purchase manager when page loads
let purchaseManager;
document.addEventListener('DOMContentLoaded', () => {
    purchaseManager = new PurchaseManager();
});