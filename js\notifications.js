// Lightweight Notification Manager for Aanabi Pharmacy v2.0
// Optimized for performance with minimal overhead

class LightweightNotificationManager {
    constructor() {
        this.isInitialized = false;
        this.notifications = [];
        this.maxNotifications = 50; // Limit to prevent memory issues
    }

    async init() {
        try {
            console.log('🔔 Initializing notification system...');
            
            // Wait for database if available
            if (window.dbManager && window.dbManager.isReady()) {
                await this.loadNotifications();
            }
            
            this.isInitialized = true;
            console.log('✅ Notification system ready');
        } catch (error) {
            console.warn('Notification system init failed:', error);
            this.isInitialized = true; // Continue without database
        }
    }

    async loadNotifications() {
        try {
            // Load active notifications from database
            const dbNotifications = await dbManager.getAll('notifications');
            this.notifications = dbNotifications
                .filter(n => n.status === 'Active' && !this.isExpired(n))
                .slice(0, this.maxNotifications);
        } catch (error) {
            console.warn('Could not load notifications from database:', error);
        }
    }

    isExpired(notification) {
        if (!notification.expires_at) return false;
        return new Date(notification.expires_at) < new Date();
    }

    // Create notification (in-memory with optional database storage)
    async create(notificationData) {
        try {
            const notification = {
                notification_id: this.generateId(),
                created_date: new Date().toISOString(),
                read_status: false,
                expires_at: this.calculateExpiry(7), // 7 days default
                status: 'Active',
                ...notificationData
            };

            // Add to memory
            this.notifications.unshift(notification);
            
            // Limit array size
            if (this.notifications.length > this.maxNotifications) {
                this.notifications = this.notifications.slice(0, this.maxNotifications);
            }

            // Save to database if available (async, non-blocking)
            if (window.dbManager && window.dbManager.isReady()) {
                dbManager.createNotification(notification).catch(console.warn);
            }

            return notification;
        } catch (error) {
            console.error('Failed to create notification:', error);
            return null;
        }
    }

    // Show notification in UI
    show(message, type = 'info', duration = 5000) {
        if (window.AanabiUtils) {
            AanabiUtils.alerts.show(message, type);
        } else {
            // Fallback alert
            this.showFallbackAlert(message, type);
        }

        // Create notification record
        this.create({
            type: type.toUpperCase(),
            title: this.getTypeTitle(type),
            message: message,
            priority: type === 'danger' ? 'High' : 'Medium'
        });
    }

    showFallbackAlert(message, type) {
        const alertContainer = document.getElementById('alerts-container');
        if (alertContainer) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = `<strong>${this.getIcon(type)}</strong> ${message}`;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => alertDiv.remove(), 5000);
        }
    }

    getIcon(type) {
        const icons = { success: '✅', warning: '⚠️', danger: '❌', info: 'ℹ️' };
        return icons[type] || 'ℹ️';
    }

    getTypeTitle(type) {
        const titles = { 
            success: 'Success', 
            warning: 'Warning', 
            danger: 'Error', 
            info: 'Information' 
        };
        return titles[type] || 'Notification';
    }

    // Mark notification as read
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.notification_id === notificationId);
        if (notification) {
            notification.read_status = true;
            notification.read_date = new Date().toISOString();
        }
    }

    // Get unread count
    getUnreadCount() {
        return this.notifications.filter(n => !n.read_status && !this.isExpired(n)).length;
    }

    // Get recent notifications
    getRecent(limit = 10) {
        return this.notifications
            .filter(n => !this.isExpired(n))
            .slice(0, limit);
    }

    // Cleanup expired notifications
    cleanup() {
        const before = this.notifications.length;
        this.notifications = this.notifications.filter(n => !this.isExpired(n));
        const cleaned = before - this.notifications.length;
        
        if (cleaned > 0) {
            console.log(`🗑️ Cleaned up ${cleaned} expired notifications`);
        }
    }

    // Generate simple ID
    generateId() {
        return 'NOT' + Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }

    calculateExpiry(days) {
        const expiry = new Date();
        expiry.setDate(expiry.getDate() + days);
        return expiry.toISOString();
    }

    // System alerts for common pharmacy events
    showStockAlert(medicineName, currentStock, minLevel) {
        this.show(
            `Low stock alert: ${medicineName} (${currentStock} remaining, minimum: ${minLevel})`,
            'warning'
        );
    }

    showExpiryAlert(medicineName, daysLeft) {
        const urgency = daysLeft <= 7 ? 'danger' : 'warning';
        this.show(
            `Expiry alert: ${medicineName} expires in ${daysLeft} days`,
            urgency
        );
    }

    showSaleSuccess(saleId, amount) {
        this.show(`Sale completed: ${saleId} - Rs. ${amount}`, 'success');
    }

    showSystemError(operation, error) {
        this.show(`System error in ${operation}: ${error}`, 'danger');
    }

    // Get stats for debugging
    getStats() {
        return {
            total: this.notifications.length,
            unread: this.getUnreadCount(),
            isInitialized: this.isInitialized
        };
    }
}

// Initialize lightweight notification manager
const notificationManager = new LightweightNotificationManager();

// Export for global use
if (typeof window !== 'undefined') {
    window.notificationManager = notificationManager;
}

console.log('🔔 Lightweight notification manager loaded');
