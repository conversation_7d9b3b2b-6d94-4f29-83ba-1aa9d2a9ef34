// Lightweight Audit Manager for Aanabi Pharmacy v2.0
// Optimized for performance with minimal impact on operations

class LightweightAuditManager {
    constructor() {
        this.isInitialized = false;
        this.auditQueue = [];
        this.maxQueueSize = 100;
        this.batchSize = 10;
        this.flushInterval = null;
    }

    async init() {
        try {
            console.log('📋 Initializing audit system...');
            
            // Start batch processing
            this.startBatchProcessing();
            
            this.isInitialized = true;
            console.log('✅ Audit system ready');
        } catch (error) {
            console.warn('Audit system init failed:', error);
            this.isInitialized = true; // Continue without full functionality
        }
    }

    // Log audit trail (non-blocking, queued processing)
    log(action, module, recordId, details = {}) {
        if (!this.isInitialized) return;

        try {
            const auditRecord = {
                log_id: this.generateId(),
                timestamp: new Date().toISOString(),
                user_id: this.getCurrentUserId(),
                user_name: this.getCurrentUserName(),
                action: action,
                module: module,
                record_id: recordId,
                details: JSON.stringify(details),
                ip_address: '127.0.0.1' // Local app
            };

            // Add to queue
            this.auditQueue.push(auditRecord);

            // Limit queue size to prevent memory issues
            if (this.auditQueue.length > this.maxQueueSize) {
                this.auditQueue = this.auditQueue.slice(-this.maxQueueSize);
            }

        } catch (error) {
            console.warn('Audit logging failed:', error);
        }
    }

    // Batch processing to reduce database impact
    startBatchProcessing() {
        // Process queue every 30 seconds
        this.flushInterval = setInterval(() => {
            this.flushQueue();
        }, 30000);

        // Also flush on page unload
        window.addEventListener('beforeunload', () => {
            this.flushQueue();
        });
    }

    async flushQueue() {
        if (this.auditQueue.length === 0) return;
        
        try {
            const batch = this.auditQueue.splice(0, this.batchSize);
            
            // Save to database if available
            if (window.dbManager && window.dbManager.isReady()) {
                for (const record of batch) {
                    try {
                        await dbManager.insertWithoutAudit('audit_logs', record);
                    } catch (error) {
                        console.warn('Failed to save audit record:', error);
                    }
                }
            }
        } catch (error) {
            console.warn('Audit flush failed:', error);
        }
    }

    // Convenience methods for common audit events
    logCreate(module, recordId, data) {
        this.log('CREATE', module, recordId, { data });
    }

    logUpdate(module, recordId, oldData, newData) {
        this.log('UPDATE', module, recordId, { oldData, newData });
    }

    logDelete(module, recordId, data) {
        this.log('DELETE', module, recordId, { data });
    }

    logLogin(username, success) {
        this.log('LOGIN', 'auth', username, { success });
    }

    logLogout(username) {
        this.log('LOGOUT', 'auth', username, {});
    }

    logSale(saleId, saleData) {
        this.log('SALE', 'sales', saleId, { 
            customer: saleData.customer_name,
            medicine: saleData.medicine_name,
            amount: saleData.total_amount
        });
    }

    logPurchase(purchaseId, purchaseData) {
        this.log('PURCHASE', 'purchases', purchaseId, {
            supplier: purchaseData.supplier_name,
            medicine: purchaseData.medicine_name,
            amount: purchaseData.total_amount
        });
    }

    logStockUpdate(medicineId, oldStock, newStock, reason) {
        this.log('STOCK_UPDATE', 'medicines', medicineId, {
            oldStock,
            newStock,
            reason
        });
    }

    logSystemEvent(event, details) {
        this.log('SYSTEM', 'system', event, details);
    }

    // Get current user info safely
    getCurrentUserId() {
        try {
            return window.authManager?.getCurrentUser()?.user_id || 'UNKNOWN';
        } catch (error) {
            return 'UNKNOWN';
        }
    }

    getCurrentUserName() {
        try {
            return window.authManager?.getCurrentUser()?.username || 'unknown';
        } catch (error) {
            return 'unknown';
        }
    }

    // Generate simple audit ID
    generateId() {
        return 'AUD' + Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    }

    // Get recent audit logs (from database)
    async getRecent(limit = 50) {
        try {
            if (window.dbManager && window.dbManager.isReady()) {
                const logs = await dbManager.getAll('audit_logs');
                return logs
                    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                    .slice(0, limit);
            }
        } catch (error) {
            console.warn('Failed to load audit logs:', error);
        }
        return [];
    }

    // Search audit logs
    async search(criteria) {
        try {
            const logs = await this.getRecent(200);
            return logs.filter(log => {
                if (criteria.action && log.action !== criteria.action) return false;
                if (criteria.module && log.module !== criteria.module) return false;
                if (criteria.user && log.user_name !== criteria.user) return false;
                if (criteria.dateFrom && new Date(log.timestamp) < new Date(criteria.dateFrom)) return false;
                if (criteria.dateTo && new Date(log.timestamp) > new Date(criteria.dateTo)) return false;
                return true;
            });
        } catch (error) {
            console.warn('Audit search failed:', error);
            return [];
        }
    }

    // Get audit statistics
    async getStats() {
        try {
            const logs = await this.getRecent(500);
            const stats = {
                total: logs.length,
                today: 0,
                thisWeek: 0,
                byAction: {},
                byModule: {},
                byUser: {}
            };

            const today = new Date().toISOString().split('T')[0];
            const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

            logs.forEach(log => {
                const logDate = new Date(log.timestamp);
                
                if (log.timestamp.startsWith(today)) stats.today++;
                if (logDate >= weekAgo) stats.thisWeek++;
                
                stats.byAction[log.action] = (stats.byAction[log.action] || 0) + 1;
                stats.byModule[log.module] = (stats.byModule[log.module] || 0) + 1;
                stats.byUser[log.user_name] = (stats.byUser[log.user_name] || 0) + 1;
            });

            return stats;
        } catch (error) {
            console.warn('Failed to generate audit stats:', error);
            return null;
        }
    }

    // Cleanup old audit logs (called periodically)
    async cleanup(retentionDays = 365) {
        try {
            if (!window.dbManager || !window.dbManager.isReady()) return;

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
            
            const logs = await dbManager.getAll('audit_logs');
            let deletedCount = 0;

            for (const log of logs) {
                if (new Date(log.timestamp) < cutoffDate) {
                    await dbManager.delete('audit_logs', log.log_id);
                    deletedCount++;
                }
            }

            if (deletedCount > 0) {
                console.log(`🗑️ Cleaned up ${deletedCount} old audit logs`);
            }
        } catch (error) {
            console.warn('Audit cleanup failed:', error);
        }
    }

    // Export audit data for compliance
    async export(format = 'json') {
        try {
            const logs = await this.getRecent(1000);
            
            if (format === 'csv') {
                return this.convertToCSV(logs);
            } else {
                return JSON.stringify(logs, null, 2);
            }
        } catch (error) {
            console.warn('Audit export failed:', error);
            return null;
        }
    }

    convertToCSV(logs) {
        const headers = ['Timestamp', 'User', 'Action', 'Module', 'Record ID', 'Details'];
        const rows = logs.map(log => [
            log.timestamp,
            log.user_name,
            log.action,
            log.module,
            log.record_id,
            log.details
        ]);
        
        return [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
    }

    // Get debug info
    getDebugInfo() {
        return {
            isInitialized: this.isInitialized,
            queueSize: this.auditQueue.length,
            maxQueueSize: this.maxQueueSize,
            batchSize: this.batchSize,
            flushInterval: !!this.flushInterval
        };
    }

    // Cleanup method
    destroy() {
        if (this.flushInterval) {
            clearInterval(this.flushInterval);
            this.flushInterval = null;
        }
        
        // Final flush
        this.flushQueue();
        
        this.isInitialized = false;
    }
}

// Initialize lightweight audit manager
const auditManager = new LightweightAuditManager();

// Export for global use
if (typeof window !== 'undefined') {
    window.auditManager = auditManager;
}

console.log('📋 Lightweight audit manager loaded');
