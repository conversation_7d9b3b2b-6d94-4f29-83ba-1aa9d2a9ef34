// Sales management functionality

class SalesManager {
    constructor() {
        this.sales = [];
        this.medicines = [];
        this.customers = [];
        this.currentSale = null;
        this.vatRate = 13; // 13% VAT
        this.init();
    }

    async init() {
        await this.loadData();
        await this.loadSettings(); // ✨ NEW: Load system settings
        this.setupEventListeners();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    }
    
    // ✨ NEW: Load system settings
    async loadSettings() {
        try {
            const settings = await dbManager.getAll('settings');
            
            // Get tax system setting
            const taxSystemSetting = settings.find(s => s.key === 'tax_system');
            this.taxSystem = taxSystemSetting ? taxSystemSetting.value : 'vat';
            
            // Get VAT rate
            const vatRateSetting = settings.find(s => s.key === 'vat_rate');
            this.vatRate = vatRateSetting ? parseFloat(vatRateSetting.value) : 13;
            
            // Get loyalty settings
            const loyaltyRateSetting = settings.find(s => s.key === 'loyalty_points_rate');
            this.loyaltyPointsRate = loyaltyRateSetting ? parseFloat(loyaltyRateSetting.value) : 20; // 1 point per Rs. 20
            
            const loyaltyRedemptionSetting = settings.find(s => s.key === 'loyalty_redemption_rate');
            this.loyaltyRedemptionRate = loyaltyRedemptionSetting ? parseFloat(loyaltyRedemptionSetting.value) : 0.10; // 1 point = Rs. 0.10
            
            console.log(`⚙️ Settings loaded: Tax=${this.taxSystem}, VAT=${this.vatRate}%, Loyalty=${this.loyaltyPointsRate}:1`);
        } catch (error) {
            console.error('Error loading settings:', error);
            // Use defaults
            this.taxSystem = 'vat';
            this.vatRate = 13;
            this.loyaltyPointsRate = 20;
            this.loyaltyRedemptionRate = 0.10;
        }
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadData() {
        try {
            // Wait for database to be ready
            if (!dbManager.isReady()) {
                setTimeout(() => this.loadData(), 1000);
                return;
            }

            this.sales = await dbManager.getAll('sales');
            this.medicines = await dbManager.getAll('medicines');
            this.customers = await dbManager.getAll('customers');

            this.populateMedicineDropdowns();
            this.renderSales();
            this.updateStatistics();
        } catch (error) {
            console.error('Error loading sales data:', error);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('sales-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterSales(e.target.value));
        }

        // Form submissions
        const saleForm = document.getElementById('sale-form');
        if (saleForm) {
            saleForm.addEventListener('submit', (e) => this.handleSaleSubmit(e));
        }

        const quickSaleForm = document.getElementById('quick-sale-form');
        if (quickSaleForm) {
            quickSaleForm.addEventListener('submit', (e) => this.handleQuickSaleSubmit(e));
        }
    }

    // ✨ ENHANCED: Populate dropdowns with enhanced stock info
    populateMedicineDropdowns() {
        const activeMedicines = this.medicines.filter(m => m.status === 'Active' && m.current_stock > 0);
        
        // Regular sale dropdown
        const medicineSelect = document.getElementById('medicine-select');
        if (medicineSelect) {
            medicineSelect.innerHTML = '<option value="">Select Medicine</option>';
            activeMedicines.forEach(medicine => {
                const option = document.createElement('option');
                option.value = medicine.medicine_id;
                const regularStock = medicine.regular_stock || 0;
                const bonusStock = medicine.bonus_stock || 0;
                const loyaltyPrice = medicine.loyalty_point_price ? ` | ${medicine.loyalty_point_price} pts` : '';
                option.textContent = `${medicine.medicine_name} (R:${regularStock} + B:${bonusStock}${loyaltyPrice})`;
                medicineSelect.appendChild(option);
            });
        }

        // Quick sale dropdown
        const quickMedicineSelect = document.getElementById('quick-medicine-select');
        if (quickMedicineSelect) {
            quickMedicineSelect.innerHTML = '<option value="">Select Medicine</option>';
            activeMedicines.forEach(medicine => {
                const option = document.createElement('option');
                option.value = medicine.medicine_id;
                option.textContent = `${medicine.medicine_name} (Stock: ${medicine.current_stock})`;
                quickMedicineSelect.appendChild(option);
            });
        }
    }

    renderSales() {
        const tbody = document.getElementById('sales-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.sales.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" class="text-center text-muted">No sales found</td></tr>';
            return;
        }

        // Sort sales by date (newest first)
        const sortedSales = [...this.sales].sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));

        sortedSales.forEach(sale => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${sale.sale_id}</td>
                <td>${new Date(sale.date).toLocaleDateString()}</td>
                <td>${sale.time}</td>
                <td>${sale.customer_name || 'Walk-in Customer'}</td>
                <td>${sale.medicine_name}</td>
                <td>${sale.quantity}</td>
                <td>Rs. ${parseFloat(sale.unit_price).toFixed(2)}</td>
                <td>${sale.discount_percent}%</td>
                <td>Rs. ${parseFloat(sale.final_total).toFixed(2)}</td>
                <td><span class="status status-paid">${sale.payment_method}</span></td>
                <td>Rs. ${parseFloat(sale.profit_amount || 0).toFixed(2)}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="printSaleBill('${sale.sale_id}')">🖨️ Print</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSale('${sale.sale_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    filterSales(searchTerm) {
        const term = searchTerm.toLowerCase();
        const filteredSales = this.sales.filter(sale => 
            sale.sale_id.toLowerCase().includes(term) ||
            (sale.customer_name && sale.customer_name.toLowerCase().includes(term)) ||
            sale.medicine_name.toLowerCase().includes(term) ||
            sale.payment_method.toLowerCase().includes(term)
        );
        
        // Re-render with filtered results
        const tbody = document.getElementById('sales-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (filteredSales.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" class="text-center text-muted">No matching sales found</td></tr>';
            return;
        }

        filteredSales.forEach(sale => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${sale.sale_id}</td>
                <td>${new Date(sale.date).toLocaleDateString()}</td>
                <td>${sale.time}</td>
                <td>${sale.customer_name || 'Walk-in Customer'}</td>
                <td>${sale.medicine_name}</td>
                <td>${sale.quantity}</td>
                <td>Rs. ${parseFloat(sale.unit_price).toFixed(2)}</td>
                <td>${sale.discount_percent}%</td>
                <td>Rs. ${parseFloat(sale.final_total).toFixed(2)}</td>
                <td><span class="status status-paid">${sale.payment_method}</span></td>
                <td>Rs. ${parseFloat(sale.profit_amount || 0).toFixed(2)}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="printSaleBill('${sale.sale_id}')">🖨️ Print</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSale('${sale.sale_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStatistics() {
        const today = new Date().toISOString().split('T')[0];
        const todaySales = this.sales.filter(sale => sale.date === today);
        
        const todayAmount = todaySales.reduce((sum, sale) => sum + parseFloat(sale.final_total || 0), 0);
        const totalTransactions = todaySales.length;
        const averageSale = totalTransactions > 0 ? todayAmount / totalTransactions : 0;
        const totalProfit = todaySales.reduce((sum, sale) => sum + parseFloat(sale.profit_amount || 0), 0);

        document.getElementById('today-sales-amount').textContent = `Rs. ${todayAmount.toLocaleString()}`;
        document.getElementById('total-transactions').textContent = totalTransactions;
        document.getElementById('average-sale').textContent = `Rs. ${averageSale.toFixed(0)}`;
        document.getElementById('total-profit').textContent = `Rs. ${totalProfit.toLocaleString()}`;
    }

    async handleSaleSubmit(event) {
        event.preventDefault();

        // Check if database is ready
        if (!dbManager.isReady()) {
            this.showAlert('Database is not ready. Please wait a moment and try again.', 'warning');
            return;
        }

        const formData = new FormData(event.target);
        const saleData = Object.fromEntries(formData.entries());

        try {
            // Validate stock availability
            const medicine = this.medicines.find(m => m.medicine_id === saleData.medicine_id);
            if (!medicine) {
                this.showAlert('Medicine not found', 'danger');
                return;
            }

            const quantity = parseInt(saleData.quantity);
            if (quantity > medicine.current_stock) {
                this.showAlert(`Insufficient stock. Available: ${medicine.current_stock}`, 'danger');
                return;
            }

            // ✨ NEW: Handle loyalty points redemption
            const loyaltyPointsUsed = parseInt(saleData.loyalty_points_used) || 0;
            let customer = null;
            
            if (saleData.customer_phone) {
                const customers = await dbManager.search('customers', 'phone', saleData.customer_phone);
                customer = customers.length > 0 ? customers[0] : null;
                
                // Validate loyalty points if used
                if (loyaltyPointsUsed > 0) {
                    if (!customer) {
                        this.showAlert('Customer not found for loyalty points redemption', 'danger');
                        return;
                    }
                    if (loyaltyPointsUsed > (customer.loyalty_points || 0)) {
                        this.showAlert(`Insufficient loyalty points. Available: ${customer.loyalty_points || 0}`, 'danger');
                        return;
                    }
                }
            }

            // ✨ NEW: Enhanced sales calculation with bonus-first algorithm
            const saleCalculation = await this.calculateEnhancedSale(medicine, saleData, loyaltyPointsUsed);
            
            // Prepare enhanced sale record
            const sale = {
                sale_id: await dbManager.generateId('sales', 'SALE'),
                date: new Date().toISOString().split('T')[0],
                time: new Date().toLocaleTimeString('en-US', { hour12: false }),
                customer_name: saleData.customer_name || 'Walk-in Customer',
                customer_phone: saleData.customer_phone || '',
                medicine_id: saleData.medicine_id,
                medicine_name: medicine.medicine_name,
                
                // ✨ NEW: Enhanced quantity tracking
                quantity: quantity,
                bonus_qty_sold: saleCalculation.bonusQtySold,
                regular_qty_sold: saleCalculation.regularQtySold,
                
                // Pricing
                unit_price: saleCalculation.unitPrice,
                total_amount: saleCalculation.totalAmount,
                discount_percent: saleCalculation.discountPercent,
                discount_amount: saleCalculation.discountAmount,
                
                // ✨ NEW: Loyalty points integration
                loyalty_points_used: loyaltyPointsUsed,
                loyalty_points_value: loyaltyPointsUsed * this.loyaltyRedemptionRate,
                cash_amount: saleCalculation.cashAmount,
                payment_method: saleData.payment_method,
                
                // ✨ NEW: Enhanced profit tracking
                bonus_profit: saleCalculation.bonusProfit,
                regular_profit: saleCalculation.regularProfit,
                total_profit: saleCalculation.totalProfit,
                
                // Tax calculation based on system setting
                tax_system_used: this.taxSystem,
                tax_amount: saleCalculation.taxAmount,
                final_total: saleCalculation.finalTotal,
                
                // Backward compatibility
                profit_amount: saleCalculation.totalProfit
            };

            // Save sale
            await dbManager.insert('sales', sale);

            // ✨ NEW: Update medicine stock using bonus-first algorithm
            await this.updateMedicineStockBonusFirst(medicine, quantity);

            // ✨ NEW: Update customer loyalty points
            if (customer) {
                await this.updateCustomerLoyaltyEnhanced(customer, sale);
            }

            // ✨ NEW: Create notifications for significant sales
            if (sale.final_total >= 1000) {
                await notificationManager.createNotification({
                    type: 'HIGH_VALUE_SALE',
                    title: 'High Value Sale',
                    message: `Large sale completed: Rs. ${sale.final_total.toFixed(2)} to ${sale.customer_name}`,
                    priority: 'Medium',
                    related_record_id: sale.sale_id,
                    related_module: 'sales'
                });
            }

            this.showAlert('Sale completed successfully!', 'success');
            this.closeSaleModal();
            await this.loadData();

            // Show print option
            if (confirm('Sale completed! Do you want to print the bill?')) {
                this.printEnhancedBill(sale);
            }

        } catch (error) {
            console.error('Error processing sale:', error);
            this.showAlert('Error processing sale. Please try again.', 'danger');
        }
    }

    async handleQuickSaleSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const saleData = Object.fromEntries(formData.entries());
        
        try {
            const medicine = this.medicines.find(m => m.medicine_id === saleData.medicine_id);
            if (!medicine) {
                this.showAlert('Medicine not found', 'danger');
                return;
            }

            const quantity = parseInt(saleData.quantity);
            if (quantity > medicine.current_stock) {
                this.showAlert(`Insufficient stock. Available: ${medicine.current_stock}`, 'danger');
                return;
            }

            const unitPrice = parseFloat(medicine.selling_price);
            const totalAmount = quantity * unitPrice;
            const taxAmount = (totalAmount * this.vatRate) / 100;
            const finalTotal = totalAmount + taxAmount;
            const profitAmount = (unitPrice - parseFloat(medicine.purchase_price)) * quantity;

            const sale = {
                sale_id: await dbManager.generateId('sales', 'SALE'),
                date: new Date().toISOString().split('T')[0],
                time: new Date().toLocaleTimeString('en-US', { hour12: false }),
                customer_name: 'Walk-in Customer',
                customer_phone: '',
                medicine_id: saleData.medicine_id,
                medicine_name: medicine.medicine_name,
                quantity: quantity,
                unit_price: unitPrice,
                total_amount: totalAmount,
                discount_percent: 0,
                discount_amount: 0,
                tax_amount: taxAmount,
                final_total: finalTotal,
                payment_method: saleData.payment_method,
                profit_amount: profitAmount
            };

            await dbManager.insert('sales', sale);

            medicine.current_stock -= quantity;
            await dbManager.update('medicines', medicine);

            this.showAlert('Quick sale completed!', 'success');
            this.closeQuickSaleModal();
            await this.loadData();

        } catch (error) {
            console.error('Error processing quick sale:', error);
            this.showAlert('Error processing sale. Please try again.', 'danger');
        }
    }

    // ✨ NEW: Enhanced sales calculation with bonus-first algorithm
    async calculateEnhancedSale(medicine, saleData, loyaltyPointsUsed) {
        const quantity = parseInt(saleData.quantity);
        const unitPrice = parseFloat(medicine.selling_price);
        const discountPercent = parseFloat(saleData.discount_percent) || 0;
        
        // 🎁 BONUS-FIRST ALGORITHM: Sell bonus stock before regular stock
        const availableBonusStock = medicine.bonus_stock || 0;
        const availableRegularStock = medicine.regular_stock || 0;
        
        let bonusQtySold = 0;
        let regularQtySold = 0;
        
        if (quantity <= availableBonusStock) {
            // All from bonus stock
            bonusQtySold = quantity;
            regularQtySold = 0;
        } else {
            // Use all bonus stock, rest from regular stock
            bonusQtySold = availableBonusStock;
            regularQtySold = quantity - availableBonusStock;
        }
        
        console.log(`🎁 Bonus-first allocation: ${bonusQtySold} bonus + ${regularQtySold} regular = ${quantity} total`);
        
        // Calculate base amounts
        const totalAmount = quantity * unitPrice;
        const discountAmount = (totalAmount * discountPercent) / 100;
        const afterDiscount = totalAmount - discountAmount;
        
        // Calculate loyalty points value
        const loyaltyPointsValue = loyaltyPointsUsed * this.loyaltyRedemptionRate;
        const afterLoyaltyDiscount = Math.max(0, afterDiscount - loyaltyPointsValue);
        
        // Calculate tax based on system setting
        let taxAmount = 0;
        if (this.taxSystem === 'vat') {
            taxAmount = (afterLoyaltyDiscount * this.vatRate) / 100;
        }
        // PAN system has no additional tax
        
        const finalTotal = afterLoyaltyDiscount + taxAmount;
        const cashAmount = finalTotal; // Amount paid in cash after loyalty points
        
        // 💰 ENHANCED PROFIT CALCULATION
        const purchasePrice = parseFloat(medicine.purchase_price) || 0;
        
        // Bonus profit: 100% margin (no cost)
        const bonusProfit = bonusQtySold * unitPrice;
        
        // Regular profit: normal margin
        const regularProfit = regularQtySold * (unitPrice - purchasePrice);
        
        // Total profit
        const totalProfit = bonusProfit + regularProfit;
        
        console.log(`💰 Profit breakdown: Bonus Rs.${bonusProfit} + Regular Rs.${regularProfit} = Total Rs.${totalProfit}`);
        
        return {
            bonusQtySold,
            regularQtySold,
            unitPrice,
            totalAmount,
            discountPercent,
            discountAmount,
            loyaltyPointsValue,
            cashAmount,
            taxAmount,
            finalTotal,
            bonusProfit,
            regularProfit,
            totalProfit
        };
    }
    
    // ✨ NEW: Update medicine stock using bonus-first algorithm
    async updateMedicineStockBonusFirst(medicine, quantitySold) {
        const availableBonusStock = medicine.bonus_stock || 0;
        const availableRegularStock = medicine.regular_stock || 0;
        
        let bonusQtySold = 0;
        let regularQtySold = 0;
        
        if (quantitySold <= availableBonusStock) {
            // All from bonus stock
            bonusQtySold = quantitySold;
            regularQtySold = 0;
        } else {
            // Use all bonus stock, rest from regular stock
            bonusQtySold = availableBonusStock;
            regularQtySold = quantitySold - availableBonusStock;
        }
        
        // Update stock levels
        medicine.bonus_stock = Math.max(0, availableBonusStock - bonusQtySold);
        medicine.regular_stock = Math.max(0, availableRegularStock - regularQtySold);
        medicine.current_stock = medicine.bonus_stock + medicine.regular_stock;
        
        // Update last sale info
        medicine.last_sale_date = new Date().toISOString().split('T')[0];
        
        await dbManager.update('medicines', medicine);
        
        console.log(`📋 Stock updated: ${medicine.medicine_name} - Bonus: ${medicine.bonus_stock}, Regular: ${medicine.regular_stock}`);
        
        // Create low stock notification if needed
        if (medicine.current_stock <= medicine.min_stock_level) {
            await notificationManager.createNotification({
                type: 'STOCK_ALERT',
                title: 'Low Stock After Sale',
                message: `${medicine.medicine_name} is now below minimum stock level (${medicine.current_stock}/${medicine.min_stock_level})`,
                priority: medicine.current_stock === 0 ? 'High' : 'Medium',
                related_record_id: medicine.medicine_id,
                related_module: 'medicines'
            });
        }
    }
    
    // ✨ NEW: Enhanced customer loyalty management
    async updateCustomerLoyaltyEnhanced(customer, sale) {
        try {
            // Deduct used loyalty points
            customer.loyalty_points = Math.max(0, (customer.loyalty_points || 0) - sale.loyalty_points_used);
            
            // Add new loyalty points based on cash amount spent
            const newPoints = Math.floor(sale.cash_amount / this.loyaltyPointsRate);
            customer.loyalty_points += newPoints;
            
            // Update purchase history
            customer.total_purchase_amount = (parseFloat(customer.total_purchase_amount) || 0) + sale.final_total;
            customer.last_purchase_date = sale.date;
            
            await dbManager.update('customers', customer);
            
            console.log(`🏆 Loyalty updated: ${customer.customer_name} - Used: ${sale.loyalty_points_used}, Earned: ${newPoints}, Balance: ${customer.loyalty_points}`);
            
            // Create notification for significant point milestones
            if (customer.loyalty_points >= 1000 && customer.loyalty_points - newPoints < 1000) {
                await notificationManager.createNotification({
                    type: 'LOYALTY_MILESTONE',
                    title: 'Loyalty Milestone Reached',
                    message: `${customer.customer_name} has reached 1000+ loyalty points!`,
                    priority: 'Low',
                    related_record_id: customer.customer_id,
                    related_module: 'customers'
                });
            }
            
        } catch (error) {
            console.error('Error updating customer loyalty:', error);
        }
    }
    
    // ✨ NEW: Enhanced bill printing with bonus/loyalty information
    printEnhancedBill(sale) {
        const bonusInfo = sale.bonus_qty_sold > 0 ? ` (${sale.bonus_qty_sold}B+${sale.regular_qty_sold}R)` : '';
        const loyaltyInfo = sale.loyalty_points_used > 0 ? `
                    <div style="display: flex; justify-content: space-between;">
                        <span>Loyalty Points Used (${sale.loyalty_points_used} pts):</span>
                        <span>- Rs. ${parseFloat(sale.loyalty_points_value).toFixed(2)}</span>
                    </div>` : '';
        
        const taxLabel = sale.tax_system_used === 'vat' ? 'VAT (13%)' : 'Tax (PAN)';
        
        const billContent = `
            <div style="max-width: 400px; margin: 0 auto; font-family: monospace; font-size: 12px;">
                <div style="text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px;">
                    <h2 style="margin: 0;">🏥 AANABI PHARMACY v2.0</h2>
                    <p style="margin: 5px 0;">Main Street, Kathmandu, Nepal</p>
                    <p style="margin: 5px 0;">Phone: 01-4567890</p>
                    <p style="margin: 5px 0;">Email: <EMAIL></p>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Bill No: ${sale.sale_id}</span>
                        <span>Date: ${new Date(sale.date).toLocaleDateString()}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Customer: ${sale.customer_name}</span>
                        <span>Time: ${sale.time}</span>
                    </div>
                </div>
                
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                    <thead>
                        <tr style="border-bottom: 1px solid #000;">
                            <th style="text-align: left; padding: 5px;">Item</th>
                            <th style="text-align: center; padding: 5px;">Qty</th>
                            <th style="text-align: right; padding: 5px;">Rate</th>
                            <th style="text-align: right; padding: 5px;">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 5px;">${sale.medicine_name}</td>
                            <td style="text-align: center; padding: 5px;">${sale.quantity}${bonusInfo}</td>
                            <td style="text-align: right; padding: 5px;">Rs. ${parseFloat(sale.unit_price).toFixed(2)}</td>
                            <td style="text-align: right; padding: 5px;">Rs. ${parseFloat(sale.total_amount).toFixed(2)}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="border-top: 1px solid #000; padding-top: 10px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Subtotal:</span>
                        <span>Rs. ${parseFloat(sale.total_amount).toFixed(2)}</span>
                    </div>
                    ${sale.discount_amount > 0 ? `
                    <div style="display: flex; justify-content: space-between;">
                        <span>Discount (${sale.discount_percent}%):</span>
                        <span>- Rs. ${parseFloat(sale.discount_amount).toFixed(2)}</span>
                    </div>` : ''}
                    ${loyaltyInfo}
                    ${sale.tax_amount > 0 ? `
                    <div style="display: flex; justify-content: space-between;">
                        <span>${taxLabel}:</span>
                        <span>Rs. ${parseFloat(sale.tax_amount).toFixed(2)}</span>
                    </div>` : ''}
                    <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 14px; border-top: 1px solid #000; padding-top: 5px;">
                        <span>TOTAL:</span>
                        <span>Rs. ${parseFloat(sale.final_total).toFixed(2)}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px;">
                        <span>Payment:</span>
                        <span>${sale.payment_method}</span>
                    </div>
                    ${sale.bonus_qty_sold > 0 ? `
                    <div style="margin-top: 10px; padding: 5px; background: #f0f8ff; border-radius: 3px; font-size: 10px;">
                        <strong>Profit Breakdown:</strong><br>
                        Bonus Stock Profit: Rs. ${parseFloat(sale.bonus_profit).toFixed(2)}<br>
                        Regular Stock Profit: Rs. ${parseFloat(sale.regular_profit).toFixed(2)}<br>
                        <strong>Total Profit: Rs. ${parseFloat(sale.total_profit).toFixed(2)}</strong>
                    </div>` : ''}
                </div>
                
                <div style="text-align: center; margin-top: 20px; border-top: 1px solid #000; padding-top: 10px;">
                    <p style="margin: 5px 0;">Thank you for your purchase!</p>
                    <p style="margin: 5px 0;">Take care of your health</p>
                    <p style="margin: 5px 0; font-size: 10px;">Enhanced System v2.0 | ${new Date().toLocaleString()}</p>
                </div>
            </div>
        `;

        document.getElementById('bill-content').innerHTML = billContent;
        document.getElementById('bill-modal').style.display = 'flex';
    }

    async updateCustomerLoyalty(phone, amount) {
        try {
            const customers = await dbManager.search('customers', 'phone', phone);
            if (customers.length > 0) {
                const customer = customers[0];
                customer.total_purchase_amount = (parseFloat(customer.total_purchase_amount) || 0) + amount;
                customer.loyalty_points = Math.floor(customer.total_purchase_amount / 20); // 1 point per Rs. 20
                customer.last_purchase_date = new Date().toISOString().split('T')[0];
                await dbManager.update('customers', customer);
            }
        } catch (error) {
            console.error('Error updating customer loyalty:', error);
        }
    }

    printBill(sale) {
        const billContent = `
            <div style="max-width: 400px; margin: 0 auto; font-family: monospace; font-size: 12px;">
                <div style="text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px;">
                    <h2 style="margin: 0;">🏥 AANABI PHARMACY</h2>
                    <p style="margin: 5px 0;">Main Street, Kathmandu, Nepal</p>
                    <p style="margin: 5px 0;">Phone: 01-4567890</p>
                    <p style="margin: 5px 0;">Email: <EMAIL></p>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Bill No: ${sale.sale_id}</span>
                        <span>Date: ${new Date(sale.date).toLocaleDateString()}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Customer: ${sale.customer_name}</span>
                        <span>Time: ${sale.time}</span>
                    </div>
                </div>
                
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
                    <thead>
                        <tr style="border-bottom: 1px solid #000;">
                            <th style="text-align: left; padding: 5px;">Item</th>
                            <th style="text-align: center; padding: 5px;">Qty</th>
                            <th style="text-align: right; padding: 5px;">Rate</th>
                            <th style="text-align: right; padding: 5px;">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 5px;">${sale.medicine_name}</td>
                            <td style="text-align: center; padding: 5px;">${sale.quantity}</td>
                            <td style="text-align: right; padding: 5px;">Rs. ${parseFloat(sale.unit_price).toFixed(2)}</td>
                            <td style="text-align: right; padding: 5px;">Rs. ${parseFloat(sale.total_amount).toFixed(2)}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="border-top: 1px solid #000; padding-top: 10px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span>Subtotal:</span>
                        <span>Rs. ${parseFloat(sale.total_amount).toFixed(2)}</span>
                    </div>
                    ${sale.discount_amount > 0 ? `
                    <div style="display: flex; justify-content: space-between;">
                        <span>Discount (${sale.discount_percent}%):</span>
                        <span>- Rs. ${parseFloat(sale.discount_amount).toFixed(2)}</span>
                    </div>` : ''}
                    <div style="display: flex; justify-content: space-between;">
                        <span>VAT (13%):</span>
                        <span>Rs. ${parseFloat(sale.tax_amount).toFixed(2)}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 14px; border-top: 1px solid #000; padding-top: 5px;">
                        <span>TOTAL:</span>
                        <span>Rs. ${parseFloat(sale.final_total).toFixed(2)}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px;">
                        <span>Payment:</span>
                        <span>${sale.payment_method}</span>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px; border-top: 1px solid #000; padding-top: 10px;">
                    <p style="margin: 5px 0;">Thank you for your purchase!</p>
                    <p style="margin: 5px 0;">Take care of your health</p>
                    <p style="margin: 5px 0; font-size: 10px;">Generated on ${new Date().toLocaleString()}</p>
                </div>
            </div>
        `;

        document.getElementById('bill-content').innerHTML = billContent;
        document.getElementById('bill-modal').style.display = 'flex';
    }

    printBillContent() {
        const billContent = document.getElementById('bill-content').innerHTML;
        const printWindow = window.open('', '_blank', 'width=600,height=800');
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Sales Receipt - Aanabi Pharmacy</title>
                <style>
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                    body {
                        font-family: monospace;
                        font-size: 12px;
                        margin: 20px;
                    }
                    .print-button {
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        padding: 10px 20px;
                        background: #3498db;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                    .print-button:hover {
                        background: #2980b9;
                    }
                </style>
            </head>
            <body>
                <button class="print-button no-print" onclick="window.print(); window.close();">🖨️ Print Bill</button>
                ${billContent}
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
    }

    showAlert(message, type) {
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        setTimeout(() => alert.remove(), 3000);
    }

    closeSaleModal() {
        document.getElementById('sale-modal').style.display = 'none';
        document.getElementById('sale-form').reset();
    }

    closeQuickSaleModal() {
        document.getElementById('quick-sale-modal').style.display = 'none';
        document.getElementById('quick-sale-form').reset();
    }

    closeBillModal() {
        document.getElementById('bill-modal').style.display = 'none';
    }
}

// Global functions
function showNewSaleModal() {
    document.getElementById('sale-modal').style.display = 'flex';
    document.getElementById('sale-form').reset();
}

function showQuickSaleModal() {
    document.getElementById('quick-sale-modal').style.display = 'flex';
    document.getElementById('quick-sale-form').reset();
}

function closeSaleModal() {
    salesManager.closeSaleModal();
}

function closeQuickSaleModal() {
    salesManager.closeQuickSaleModal();
}

function closeBillModal() {
    salesManager.closeBillModal();
}

async function searchCustomer() {
    const phone = document.getElementById('customer-phone').value;
    if (phone.length >= 10) {
        try {
            const customers = await dbManager.search('customers', 'phone', phone);
            if (customers.length > 0) {
                const customer = customers[0];
                document.getElementById('customer-name').value = customer.customer_name;
            }
        } catch (error) {
            console.error('Error searching customer:', error);
        }
    }
}

function loadMedicineDetails() {
    const medicineId = document.getElementById('medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    
    if (medicine) {
        document.getElementById('available-stock').value = medicine.current_stock;
        document.getElementById('unit-price').value = medicine.selling_price;
        document.getElementById('quantity').max = medicine.current_stock;
        calculateTotal();
    } else {
        document.getElementById('available-stock').value = '';
        document.getElementById('unit-price').value = '';
        document.getElementById('quantity').max = '';
    }
}

function loadQuickMedicineDetails() {
    const medicineId = document.getElementById('quick-medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    
    if (medicine) {
        document.getElementById('quick-available-stock').value = medicine.current_stock;
        document.getElementById('quick-unit-price').value = `Rs. ${medicine.selling_price}`;
        document.getElementById('quick-quantity').max = medicine.current_stock;
        calculateQuickTotal();
    } else {
        document.getElementById('quick-available-stock').value = '';
        document.getElementById('quick-unit-price').value = '';
        document.getElementById('quick-quantity').max = '';
    }
}

function calculateTotal() {
    const unitPrice = parseFloat(document.getElementById('unit-price').value) || 0;
    const quantity = parseInt(document.getElementById('quantity').value) || 0;
    const discountPercent = parseFloat(document.getElementById('discount-percent').value) || 0;
    
    const totalAmount = unitPrice * quantity;
    const discountAmount = (totalAmount * discountPercent) / 100;
    const afterDiscount = totalAmount - discountAmount;
    const taxAmount = (afterDiscount * salesManager.vatRate) / 100;
    const finalTotal = afterDiscount + taxAmount;
    
    // Get medicine to calculate profit
    const medicineId = document.getElementById('medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    const profitAmount = medicine ? (unitPrice - parseFloat(medicine.purchase_price)) * quantity : 0;
    
    document.getElementById('total-amount').value = `Rs. ${totalAmount.toFixed(2)}`;
    document.getElementById('discount-amount').value = `Rs. ${discountAmount.toFixed(2)}`;
    document.getElementById('tax-amount').value = `Rs. ${taxAmount.toFixed(2)}`;
    document.getElementById('final-total').value = `Rs. ${finalTotal.toFixed(2)}`;
    document.getElementById('profit-amount').value = `Rs. ${profitAmount.toFixed(2)}`;
}

function calculateQuickTotal() {
    const medicineId = document.getElementById('quick-medicine-select').value;
    const medicine = salesManager.medicines.find(m => m.medicine_id === medicineId);
    const quantity = parseInt(document.getElementById('quick-quantity').value) || 0;
    
    if (medicine && quantity > 0) {
        const unitPrice = parseFloat(medicine.selling_price);
        const totalAmount = unitPrice * quantity;
        const taxAmount = (totalAmount * salesManager.vatRate) / 100;
        const finalTotal = totalAmount + taxAmount;
        
        document.getElementById('quick-total').value = `Rs. ${finalTotal.toFixed(2)}`;
    } else {
        document.getElementById('quick-total').value = '';
    }
}

async function printSaleBill(saleId) {
    try {
        const sale = await dbManager.get('sales', saleId);
        if (sale) {
            salesManager.printBill(sale);
        }
    } catch (error) {
        console.error('Error loading sale for print:', error);
        alert('Error loading sale data');
    }
}

function printBill() {
    salesManager.printBillContent();
}

function printBillFromModal() {
    salesManager.printBillContent();
}

async function deleteSale(saleId) {
    if (!confirm('Are you sure you want to delete this sale? This action cannot be undone.')) {
        return;
    }

    try {
        // Get sale details to restore stock
        const sale = await dbManager.get('sales', saleId);
        if (sale) {
            // Restore medicine stock
            const medicine = await dbManager.get('medicines', sale.medicine_id);
            if (medicine) {
                medicine.current_stock += sale.quantity;
                await dbManager.update('medicines', medicine);
            }
        }

        await dbManager.delete('sales', saleId);
        salesManager.showAlert('Sale deleted successfully!', 'success');
        await salesManager.loadData();
    } catch (error) {
        console.error('Error deleting sale:', error);
        salesManager.showAlert('Error deleting sale. Please try again.', 'danger');
    }
}

function exportSales() {
    try {
        const headers = [
            'Sale ID', 'Date', 'Time', 'Customer Name', 'Customer Phone',
            'Medicine Name', 'Quantity', 'Unit Price', 'Total Amount',
            'Discount %', 'Discount Amount', 'Tax Amount', 'Final Total',
            'Payment Method', 'Profit Amount'
        ];
        
        let csvContent = headers.join(',') + '\n';
        
        salesManager.sales.forEach(sale => {
            const row = [
                sale.sale_id,
                sale.date,
                sale.time,
                `"${sale.customer_name || ''}"`,
                sale.customer_phone || '',
                `"${sale.medicine_name}"`,
                sale.quantity,
                sale.unit_price,
                sale.total_amount,
                sale.discount_percent,
                sale.discount_amount,
                sale.tax_amount,
                sale.final_total,
                sale.payment_method,
                sale.profit_amount
            ];
            csvContent += row.join(',') + '\n';
        });
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `sales_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        salesManager.showAlert('Sales data exported successfully!', 'success');
    } catch (error) {
        console.error('Error exporting sales:', error);
        salesManager.showAlert('Error exporting sales data. Please try again.', 'danger');
    }
}

// Initialize sales manager when page loads
let salesManager;
document.addEventListener('DOMContentLoaded', () => {
    salesManager = new SalesManager();
});