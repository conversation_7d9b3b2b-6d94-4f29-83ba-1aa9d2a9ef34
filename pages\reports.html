<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Reports - Aanabi Pharmacy v2.0</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        /* Enhanced Reports Styles */
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user-details {
            display: flex;
            flex-direction: column;
            font-size: 0.9rem;
        }

        .user-name {
            font-weight: 600;
        }

        .user-role {
            opacity: 0.8;
            font-size: 0.8rem;
            text-transform: capitalize;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        /* Enhanced Analytics Cards */
        .analytics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .analytics-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .analytics-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .analytics-card.bonus {
            border-left-color: #27ae60;
        }

        .analytics-card.regular {
            border-left-color: #3498db;
        }

        .analytics-card.total {
            border-left-color: #f39c12;
        }

        .analytics-card.loyalty {
            border-left-color: #9b59b6;
        }

        .analytics-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .analytics-title {
            font-size: 0.9rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .analytics-icon {
            font-size: 1.5rem;
        }

        .analytics-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .analytics-subtitle {
            font-size: 0.8rem;
            color: #95a5a6;
        }

        .analytics-change {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .analytics-change.positive {
            color: #27ae60;
        }

        .analytics-change.negative {
            color: #e74c3c;
        }

        /* Enhanced Report Tabs */
        .report-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .report-tab {
            flex: 1;
            padding: 1rem;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #6c757d;
        }

        .report-tab:hover {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .report-tab.active {
            background: #3498db;
            color: white;
        }

        /* Profit Breakdown Section */
        .profit-breakdown-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
        }

        .profit-breakdown-title {
            text-align: center;
            margin-bottom: 2rem;
            color: #2c3e50;
        }

        .profit-items {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .profit-item {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .profit-item.bonus {
            border-left: 4px solid #27ae60;
        }

        .profit-item.regular {
            border-left: 4px solid #3498db;
        }

        .profit-item.total {
            border-left: 4px solid #f39c12;
        }

        .profit-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .profit-value.bonus {
            color: #27ae60;
        }

        .profit-value.regular {
            color: #3498db;
        }

        .profit-value.total {
            color: #f39c12;
        }

        .profit-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-bottom: 0.5rem;
        }

        .profit-percentage {
            font-size: 0.8rem;
            color: #95a5a6;
        }

        /* Enhanced Tables */
        .enhanced-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .enhanced-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 1rem;
            border-bottom: 2px solid #dee2e6;
        }

        .enhanced-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .enhanced-table tr:hover {
            background: #f8f9fa;
        }

        /* Date Range Picker Enhancement */
        .date-range-container {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .period-buttons {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .period-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .period-btn:hover {
            background: #e9ecef;
        }

        .period-btn.active {
            background: #3498db;
            color: white;
            border-color: #2980b9;
        }

        /* Enhanced Chart Containers */
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            text-align: center;
        }

        /* Loyalty Analytics */
        .loyalty-analytics {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 2px solid #f39c12;
        }

        .loyalty-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .loyalty-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .loyalty-stat {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .loyalty-stat-value {
            font-size: 1.3rem;
            font-weight: bold;
            color: #d35400;
            margin-bottom: 0.5rem;
        }

        .loyalty-stat-label {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        /* Permission-based hiding */
        .admin-only {
            display: none;
        }

        body.admin .admin-only {
            display: block;
        }

        body.admin .admin-only.inline {
            display: inline-block;
        }

        body.admin .admin-only.flex {
            display: flex;
        }

        .staff-limited {
            opacity: 0.6;
        }

        body.staff .staff-limited {
            pointer-events: none;
        }

        /* Loading states */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            color: #2c3e50;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Export Options */
        .export-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .export-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .export-btn {
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-critical {
            background-color: #e74c3c;
        }

        .status-low {
            background-color: #f39c12;
        }

        .status-good {
            background-color: #27ae60;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .analytics-overview {
                grid-template-columns: 1fr;
            }
            
            .profit-items {
                grid-template-columns: 1fr;
            }
            
            .export-buttons {
                grid-template-columns: 1fr;
            }
            
            .report-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>📊 Aanabi Reports</h3>
            <p>Loading enhanced analytics system...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                🏥 Aanabi Pharmacy v2.0
                <span style="font-size: 0.7rem; opacity: 0.8; margin-left: 0.5rem;">Enhanced Analytics</span>
            </div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
                
                <!-- User Info -->
                <div class="user-info" id="user-info" style="display: none;">
                    <div class="user-avatar" id="user-avatar">A</div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Admin</div>
                        <div class="user-role" id="user-role">Administrator</div>
                    </div>
                    <button class="logout-btn" onclick="handleLogout()">🚪 Logout</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item admin-only"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item admin-only"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link active">📊 Reports</a></li>
                <li class="nav-item admin-only"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">📊 Enhanced Reports & Analytics</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <p class="text-muted">Comprehensive business intelligence with bonus profit analysis and loyalty insights</p>
                    </div>
                    <div class="col-4">
                        <div class="form-group">
                            <label class="form-label">Report Period</label>
                            <select id="report-period" class="form-select" onchange="updateEnhancedReports()">
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month" selected>This Month</option>
                                <option value="quarter">This Quarter</option>
                                <option value="year">This Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div id="custom-date-range" class="row" style="display: none;">
                    <div class="col-4">
                        <div class="form-group">
                            <label class="form-label">From Date</label>
                            <input type="date" id="from-date" class="form-input">
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="form-group">
                            <label class="form-label">To Date</label>
                            <input type="date" id="to-date" class="form-input">
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="form-group">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary" onclick="updateEnhancedReports()">📊 Generate Report</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Analytics Overview with Bonus/Regular Breakdown -->
        <div class="analytics-overview">
            <div class="analytics-card bonus">
                <div class="analytics-header">
                    <span class="analytics-title">Bonus Profit</span>
                    <span class="analytics-icon">🎁</span>
                </div>
                <div class="analytics-value" id="period-bonus-profit">Rs. 0</div>
                <div class="analytics-subtitle">From free inventory (100% margin)</div>
                <div class="analytics-change positive" id="bonus-profit-change">+0% from last period</div>
            </div>

            <div class="analytics-card regular">
                <div class="analytics-header">
                    <span class="analytics-title">Regular Profit</span>
                    <span class="analytics-icon">📦</span>
                </div>
                <div class="analytics-value" id="period-regular-profit">Rs. 0</div>
                <div class="analytics-subtitle">From paid inventory</div>
                <div class="analytics-change positive" id="regular-profit-change">+0% from last period</div>
            </div>

            <div class="analytics-card total">
                <div class="analytics-header">
                    <span class="analytics-title">Total Revenue</span>
                    <span class="analytics-icon">💰</span>
                </div>
                <div class="analytics-value" id="period-total-revenue">Rs. 0</div>
                <div class="analytics-subtitle" id="period-transactions">0 transactions</div>
                <div class="analytics-change positive" id="revenue-change">+0% from last period</div>
            </div>

            <div class="analytics-card loyalty">
                <div class="analytics-header">
                    <span class="analytics-title">Loyalty Impact</span>
                    <span class="analytics-icon">🏆</span>
                </div>
                <div class="analytics-value" id="period-loyalty-impact">Rs. 0</div>
                <div class="analytics-subtitle">Points redeemed value</div>
                <div class="analytics-change positive" id="loyalty-change">0 points used</div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Profit Breakdown Analysis -->
        <div class="profit-breakdown-section">
            <h2 class="profit-breakdown-title">📈 Enhanced Profit Analysis</h2>
            <div class="profit-items">
                <div class="profit-item bonus">
                    <div class="profit-value bonus" id="breakdown-bonus-amount">Rs. 0</div>
                    <div class="profit-label">Bonus Stock Profit</div>
                    <div class="profit-percentage" id="bonus-percentage">0% of total</div>
                </div>
                <div class="profit-item regular">
                    <div class="profit-value regular" id="breakdown-regular-amount">Rs. 0</div>
                    <div class="profit-label">Regular Stock Profit</div>
                    <div class="profit-percentage" id="regular-percentage">0% of total</div>
                </div>
                <div class="profit-item total">
                    <div class="profit-value total" id="breakdown-total-amount">Rs. 0</div>
                    <div class="profit-label">Combined Profit</div>
                    <div class="profit-percentage" id="effective-margin">0% effective margin</div>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Loyalty Analytics Section -->
        <div class="loyalty-analytics">
            <div class="loyalty-header">
                <span style="font-size: 1.5rem;">🏆</span>
                <h3 style="margin: 0; color: #d35400;">Loyalty Program Analytics</h3>
            </div>
            <div class="loyalty-stats">
                <div class="loyalty-stat">
                    <div class="loyalty-stat-value" id="total-loyalty-customers">0</div>
                    <div class="loyalty-stat-label">Loyalty Customers</div>
                </div>
                <div class="loyalty-stat">
                    <div class="loyalty-stat-value" id="total-points-outstanding">0</div>
                    <div class="loyalty-stat-label">Points Outstanding</div>
                </div>
                <div class="loyalty-stat">
                    <div class="loyalty-stat-value" id="points-redeemed-period">0</div>
                    <div class="loyalty-stat-label">Points Redeemed</div>
                </div>
                <div class="loyalty-stat">
                    <div class="loyalty-stat-value" id="loyalty-revenue-impact">Rs. 0</div>
                    <div class="loyalty-stat-label">Revenue Impact</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Report Tabs -->
        <div class="report-tabs">
            <button class="report-tab active" onclick="showEnhancedReport('sales')">💳 Sales Analysis</button>
            <button class="report-tab" onclick="showEnhancedReport('inventory')">📦 Inventory Report</button>
            <button class="report-tab" onclick="showEnhancedReport('financial')">💰 Financial Analysis</button>
            <button class="report-tab" onclick="showEnhancedReport('customer')">👥 Customer Insights</button>
            <button class="report-tab admin-only" onclick="showEnhancedReport('supplier')">🏭 Supplier Performance</button>
        </div>

        <!-- ✨ ENHANCED: Sales Report with Bonus Analysis -->
        <div id="enhanced-sales-report" class="report-section">
            <div class="row">
                <div class="col-6">
                    <div class="chart-container">
                        <h3 class="chart-title">🎯 Top Performing Medicines</h3>
                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>Medicine</th>
                                        <th>Total Sold</th>
                                        <th>Bonus Qty</th>
                                        <th>Revenue</th>
                                        <th>Profit</th>
                                        <th>Margin</th>
                                    </tr>
                                </thead>
                                <tbody id="enhanced-top-medicines-tbody">
                                    <!-- Enhanced data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-6">
                    <div class="chart-container">
                        <h3 class="chart-title">📊 Sales by Category</h3>
                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Quantity</th>
                                        <th>Revenue</th>
                                        <th>Bonus Impact</th>
                                        <th>% of Total</th>
                                    </tr>
                                </thead>
                                <tbody id="enhanced-category-sales-tbody">
                                    <!-- Enhanced data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <h3 class="chart-title">📈 Daily Sales Trend with Profit Breakdown</h3>
                <div class="table-container">
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Transactions</th>
                                <th>Total Sales</th>
                                <th>Bonus Profit</th>
                                <th>Regular Profit</th>
                                <th>Total Profit</th>
                                <th>Loyalty Points</th>
                            </tr>
                        </thead>
                        <tbody id="enhanced-daily-sales-tbody">
                            <!-- Enhanced data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Inventory Report with Bonus Stock -->
        <div id="enhanced-inventory-report" class="report-section" style="display: none;">
            <div class="row">
                <div class="col-3">
                    <div class="analytics-card">
                        <div class="analytics-header">
                            <span class="analytics-title">Total Items</span>
                            <span class="analytics-icon">📦</span>
                        </div>
                        <div class="analytics-value" id="total-inventory-items">0</div>
                        <div class="analytics-subtitle">Active medicines</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="analytics-card bonus">
                        <div class="analytics-header">
                            <span class="analytics-title">Bonus Stock</span>
                            <span class="analytics-icon">🎁</span>
                        </div>
                        <div class="analytics-value" id="total-bonus-stock">0</div>
                        <div class="analytics-subtitle">Free inventory items</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="analytics-card">
                        <div class="analytics-header">
                            <span class="analytics-title">Inventory Value</span>
                            <span class="analytics-icon">💰</span>
                        </div>
                        <div class="analytics-value" id="total-inventory-value">Rs. 0</div>
                        <div class="analytics-subtitle">Total stock worth</div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="analytics-card">
                        <div class="analytics-header">
                            <span class="analytics-title">Critical Stock</span>
                            <span class="analytics-icon">⚠️</span>
                        </div>
                        <div class="analytics-value" id="critical-stock-items">0</div>
                        <div class="analytics-subtitle">Need immediate attention</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-6">
                    <div class="chart-container">
                        <h3 class="chart-title">🚨 Low Stock Alert</h3>
                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>Medicine</th>
                                        <th>Regular</th>
                                        <th>Bonus</th>
                                        <th>Total</th>
                                        <th>Min Level</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="enhanced-low-stock-tbody">
                                    <!-- Enhanced data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="chart-container">
                        <h3 class="chart-title">⏰ Expiring Items Alert</h3>
                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>Medicine</th>
                                        <th>Batch</th>
                                        <th>Stock Type</th>
                                        <th>Expiry Date</th>
                                        <th>Days Left</th>
                                    </tr>
                                </thead>
                                <tbody id="enhanced-expiring-tbody">
                                    <!-- Enhanced data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Financial Report with Tax System Analysis -->
        <div id="enhanced-financial-report" class="report-section" style="display: none;">
            <div class="chart-container">
                <h3 class="chart-title">💰 Enhanced Profit & Loss Statement</h3>
                <div class="row">
                    <div class="col-6">
                        <table class="enhanced-table">
                            <thead>
                                <tr>
                                    <th colspan="2" class="text-center">REVENUE BREAKDOWN</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Total Sales Revenue</td>
                                    <td class="text-right" id="pl-total-sales">Rs. 0</td>
                                </tr>
                                <tr>
                                    <td>Loyalty Points Redemption</td>
                                    <td class="text-right text-info" id="pl-loyalty-redemption">Rs. 0</td>
                                </tr>
                                <tr>
                                    <td>Discount Given</td>
                                    <td class="text-right text-danger" id="pl-total-discount">Rs. 0</td>
                                </tr>
                                <tr>
                                    <td>Tax Collected (<span id="tax-system-display">VAT 13%</span>)</td>
                                    <td class="text-right" id="pl-total-tax">Rs. 0</td>
                                </tr>
                                <tr class="table-success">
                                    <td><strong>Net Revenue</strong></td>
                                    <td class="text-right"><strong id="pl-net-revenue">Rs. 0</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="col-6">
                        <table class="enhanced-table">
                            <thead>
                                <tr>
                                    <th colspan="2" class="text-center">PROFIT ANALYSIS</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Bonus Stock Profit</td>
                                    <td class="text-right" style="color: #27ae60;" id="pl-bonus-profit">Rs. 0</td>
                                </tr>
                                <tr>
                                    <td>Regular Stock Profit</td>
                                    <td class="text-right" style="color: #3498db;" id="pl-regular-profit">Rs. 0</td>
                                </tr>
                                <tr>
                                    <td>Operating Expenses</td>
                                    <td class="text-right" id="pl-operating-expenses">Rs. 5,000</td>
                                </tr>
                                <tr class="table-warning">
                                    <td><strong>Total Expenses</strong></td>
                                    <td class="text-right"><strong id="pl-total-expenses">Rs. 5,000</strong></td>
                                </tr>
                                <tr class="table-info">
                                    <td><strong>Net Profit</strong></td>
                                    <td class="text-right"><strong id="pl-net-profit">Rs. 0</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <h3 class="chart-title">💳 Payment Method Analysis</h3>
                <div class="table-container">
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Payment Method</th>
                                <th>Transactions</th>
                                <th>Amount</th>
                                <th>Loyalty Points Used</th>
                                <th>% of Total</th>
                            </tr>
                        </thead>
                        <tbody id="enhanced-payment-method-tbody">
                            <!-- Enhanced data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Customer Analysis with Loyalty Insights -->
        <div id="enhanced-customer-report" class="report-section" style="display: none;">
            <div class="row">
                <div class="col-6">
                    <div class="chart-container">
                        <h3 class="chart-title">🏆 Top Loyalty Customers</h3>
                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Phone</th>
                                        <th>Total Purchases</th>
                                        <th>Loyalty Points</th>
                                        <th>Visits</th>
                                    </tr>
                                </thead>
                                <tbody id="enhanced-top-customers-tbody">
                                    <!-- Enhanced data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-6">
                    <div class="chart-container">
                        <h3 class="chart-title">👥 Customer Summary</h3>
                        <div class="row">
                            <div class="col-6">
                                <div class="analytics-card">
                                    <div class="analytics-value" id="total-registered-customers">0</div>
                                    <div class="analytics-subtitle">Registered Customers</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="analytics-card">
                                    <div class="analytics-value" id="new-customers-period">0</div>
                                    <div class="analytics-subtitle">New This Period</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="analytics-card">
                                    <div class="analytics-value" id="avg-customer-value">Rs. 0</div>
                                    <div class="analytics-subtitle">Avg Customer Value</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="analytics-card loyalty">
                                    <div class="analytics-value" id="loyalty-customers">0</div>
                                    <div class="analytics-subtitle">Loyalty Members</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Supplier Report (Admin Only) -->
        <div id="enhanced-supplier-report" class="report-section admin-only" style="display: none;">
            <div class="chart-container">
                <h3 class="chart-title">🏭 Enhanced Supplier Performance</h3>
                <div class="table-container">
                    <table class="enhanced-table">
                        <thead>
                            <tr>
                                <th>Supplier</th>
                                <th>Total Orders</th>
                                <th>Total Amount</th>
                                <th>Bonus Items Received</th>
                                <th>Outstanding</th>
                                <th>Last Order</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody id="enhanced-supplier-performance-tbody">
                            <!-- Enhanced data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- ✨ ENHANCED: Export Options -->
        <div class="export-section">
            <h3 style="margin-bottom: 1rem; color: #2c3e50;">📤 Enhanced Export Options</h3>
            <div class="export-buttons">
                <button class="export-btn btn btn-success" onclick="exportEnhancedReport('sales')">
                    📊 Sales Report<br>
                    <small>With bonus profit analysis</small>
                </button>
                <button class="export-btn btn btn-warning" onclick="exportEnhancedReport('inventory')">
                    📦 Inventory Report<br>
                    <small>Including bonus stock data</small>
                </button>
                <button class="export-btn btn btn-info" onclick="exportEnhancedReport('financial')">
                    💰 Financial Report<br>
                    <small>Complete P&L with tax analysis</small>
                </button>
                <button class="export-btn btn btn-primary" onclick="exportEnhancedReport('complete')">
                    📋 Complete Analytics<br>
                    <small>All reports with loyalty data</small>
                </button>
                <button class="export-btn btn btn-secondary admin-only" onclick="exportEnhancedReport('audit')">
                    🔍 Audit Report<br>
                    <small>System logs and changes</small>
                </button>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/audit.js"></script>
    <script src="../js/notifications.js"></script>
    <script src="../js/reports.js"></script>
    
    <script>
        // ✨ Enhanced Reports Page Initialization
        async function initializeEnhancedReports() {
            try {
                console.log('📊 Enhanced reports page initialization started...');
                
                // Wait for systems to be ready
                await waitForSystems();
                
                // Check authentication
                if (!authManager.isLoggedIn()) {
                    authManager.redirectToLogin('Please login to access reports');
                    return;
                }
                
                // Setup user interface
                setupUserInterface();
                
                // Initialize enhanced reports system
                await initializeEnhancedReportsSystem();
                
                // Hide loading overlay
                document.getElementById('loading-overlay').style.display = 'none';
                
                console.log('✅ Enhanced reports system initialized successfully');
                
            } catch (error) {
                console.error('❌ Reports initialization failed:', error);
                showError('Failed to initialize reports system');
            }
        }

        async function waitForSystems() {
            let attempts = 0;
            const maxAttempts = 100;

            while ((!dbManager?.isReady() || !authManager) && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!dbManager?.isReady() || !authManager) {
                throw new Error('Systems failed to initialize');
            }
        }

        function setupUserInterface() {
            const user = authManager.getCurrentUser();
            if (!user) return;

            // Update user info display
            document.getElementById('user-avatar').textContent = user.full_name.charAt(0).toUpperCase();
            document.getElementById('user-name').textContent = user.full_name;
            document.getElementById('user-role').textContent = user.role;
            document.getElementById('user-info').style.display = 'flex';

            // Set role-based styling
            document.body.className = user.role;

            // Staff users get limited access
            if (user.role === 'staff') {
                document.querySelectorAll('.staff-limited').forEach(el => {
                    el.title = 'Limited access for staff users';
                });
            }

            console.log(`📊 Enhanced reports loaded for ${user.full_name} (${user.role})`);
        }

        async function initializeEnhancedReportsSystem() {
            // Load enhanced analytics
            await loadEnhancedAnalytics();
            
            // Load profit breakdown
            await loadProfitBreakdown();
            
            // Load loyalty analytics
            await loadLoyaltyAnalytics();
            
            // Setup enhanced event listeners
            setupEnhancedEventListeners();
            
            // Load current period reports
            await updateEnhancedReports();
        }

        async function loadEnhancedAnalytics() {
            try {
                // This will be populated by the updateEnhancedReports function
                console.log('📊 Enhanced analytics framework loaded');
            } catch (error) {
                console.error('Error loading enhanced analytics:', error);
            }
        }

        async function loadProfitBreakdown() {
            try {
                const currentPeriod = document.getElementById('report-period').value;
                const sales = await dbManager.getAll('sales');
                
                // Filter sales based on current period
                const filteredSales = filterSalesByPeriod(sales, currentPeriod);
                
                let bonusProfit = 0;
                let regularProfit = 0;
                let totalProfit = 0;
                
                filteredSales.forEach(sale => {
                    bonusProfit += parseFloat(sale.bonus_profit || 0);
                    regularProfit += parseFloat(sale.regular_profit || 0);
                    totalProfit += parseFloat(sale.total_profit || sale.profit_amount || 0);
                });
                
                // Update profit breakdown display
                document.getElementById('breakdown-bonus-amount').textContent = `Rs. ${bonusProfit.toLocaleString()}`;
                document.getElementById('breakdown-regular-amount').textContent = `Rs. ${regularProfit.toLocaleString()}`;
                document.getElementById('breakdown-total-amount').textContent = `Rs. ${totalProfit.toLocaleString()}`;
                
                // Calculate percentages
                if (totalProfit > 0) {
                    const bonusPercent = Math.round((bonusProfit / totalProfit) * 100);
                    const regularPercent = Math.round((regularProfit / totalProfit) * 100);
                    
                    document.getElementById('bonus-percentage').textContent = `${bonusPercent}% of total`;
                    document.getElementById('regular-percentage').textContent = `${regularPercent}% of total`;
                    
                    // Calculate effective margin
                    const totalRevenue = filteredSales.reduce((sum, sale) => sum + parseFloat(sale.total_amount || 0), 0);
                    const effectiveMargin = totalRevenue > 0 ? Math.round((totalProfit / totalRevenue) * 100) : 0;
                    document.getElementById('effective-margin').textContent = `${effectiveMargin}% effective margin`;
                }
                
            } catch (error) {
                console.error('Error loading profit breakdown:', error);
            }
        }

        async function loadLoyaltyAnalytics() {
            try {
                const customers = await dbManager.getAll('customers');
                const sales = await dbManager.getAll('sales');
                const currentPeriod = document.getElementById('report-period').value;
                
                // Filter data by current period
                const filteredSales = filterSalesByPeriod(sales, currentPeriod);
                
                const loyaltyCustomers = customers.filter(c => (c.loyalty_points || 0) > 0);
                const totalPointsOutstanding = customers.reduce((sum, c) => sum + (c.loyalty_points || 0), 0);
                const pointsRedeemedPeriod = filteredSales.reduce((sum, s) => sum + (s.loyalty_points_used || 0), 0);
                const loyaltyRevenueImpact = filteredSales.reduce((sum, s) => sum + (s.loyalty_points_value || 0), 0);
                
                // Update loyalty analytics display
                document.getElementById('total-loyalty-customers').textContent = loyaltyCustomers.length.toLocaleString();
                document.getElementById('total-points-outstanding').textContent = totalPointsOutstanding.toLocaleString();
                document.getElementById('points-redeemed-period').textContent = pointsRedeemedPeriod.toLocaleString();
                document.getElementById('loyalty-revenue-impact').textContent = loyaltyRevenueImpact.toLocaleString();
                
            } catch (error) {
                console.error('Error loading loyalty analytics:', error);
            }
        }

        function setupEnhancedEventListeners() {
            // Report period change
            const reportPeriodSelect = document.getElementById('report-period');
            if (reportPeriodSelect) {
                reportPeriodSelect.addEventListener('change', handleReportPeriodChange);
            }
            
            // Custom date range
            const fromDate = document.getElementById('from-date');
            const toDate = document.getElementById('to-date');
            if (fromDate && toDate) {
                fromDate.addEventListener('change', updateEnhancedReports);
                toDate.addEventListener('change', updateEnhancedReports);
            }
        }

        function handleReportPeriodChange(event) {
            const period = event.target.value;
            const customDateRange = document.getElementById('custom-date-range');
            
            if (period === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
                updateEnhancedReports();
            }
        }

        async function updateEnhancedReports() {
            try {
                console.log('📊 Updating enhanced reports...');
                
                const period = document.getElementById('report-period').value;
                const sales = await dbManager.getAll('sales');
                const filteredSales = filterSalesByPeriod(sales, period);
                
                // Update enhanced analytics
                updateEnhancedAnalyticsCards(filteredSales);
                
                // Update profit breakdown
                await loadProfitBreakdown();
                
                // Update loyalty analytics
                await loadLoyaltyAnalytics();
                
                // Update current report section
                const activeReport = document.querySelector('.report-tab.active');
                if (activeReport) {
                    const reportType = activeReport.onclick.toString().match(/'([^']+)'/)[1];
                    showEnhancedReport(reportType);
                }
                
            } catch (error) {
                console.error('Error updating enhanced reports:', error);
            }
        }

        function filterSalesByPeriod(sales, period) {
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            
            switch (period) {
                case 'today':
                    return sales.filter(sale => sale.date === today);
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    return sales.filter(sale => sale.date >= weekAgo.toISOString().split('T')[0]);
                case 'month':
                    const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    return sales.filter(sale => sale.date >= monthAgo.toISOString().split('T')[0]);
                case 'quarter':
                    const quarterAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                    return sales.filter(sale => sale.date >= quarterAgo.toISOString().split('T')[0]);
                case 'year':
                    const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                    return sales.filter(sale => sale.date >= yearAgo.toISOString().split('T')[0]);
                case 'custom':
                    const fromDate = document.getElementById('from-date').value;
                    const toDate = document.getElementById('to-date').value;
                    if (fromDate && toDate) {
                        return sales.filter(sale => sale.date >= fromDate && sale.date <= toDate);
                    }
                    return sales;
                default:
                    return sales;
            }
        }

        function updateEnhancedAnalyticsCards(filteredSales) {
            let bonusProfit = 0;
            let regularProfit = 0;
            let totalRevenue = 0;
            let loyaltyImpact = 0;
            
            filteredSales.forEach(sale => {
                bonusProfit += parseFloat(sale.bonus_profit || 0);
                regularProfit += parseFloat(sale.regular_profit || 0);
                totalRevenue += parseFloat(sale.final_total || sale.total_amount || 0);
                loyaltyImpact += parseFloat(sale.loyalty_points_value || 0);
            });
            
            // Update analytics cards
            document.getElementById('period-bonus-profit').textContent = `Rs. ${bonusProfit.toLocaleString()}`;
            document.getElementById('period-regular-profit').textContent = `Rs. ${regularProfit.toLocaleString()}`;
            document.getElementById('period-total-revenue').textContent = `Rs. ${totalRevenue.toLocaleString()}`;
            document.getElementById('period-transactions').textContent = `${filteredSales.length} transactions`;
            document.getElementById('period-loyalty-impact').textContent = `Rs. ${loyaltyImpact.toLocaleString()}`;
            
            // TODO: Calculate and display change percentages
        }

        function showEnhancedReport(reportType) {
            // Hide all reports
            document.querySelectorAll('.report-section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Update tab states
            document.querySelectorAll('.report-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected report
            const reportSection = document.getElementById(`enhanced-${reportType}-report`);
            if (reportSection) {
                reportSection.style.display = 'block';
            }
            
            // Update active tab
            event.target.classList.add('active');
            
            console.log(`📊 Showing enhanced ${reportType} report`);
        }

        function exportEnhancedReport(reportType) {
            const user = authManager.getCurrentUser();
            console.log(`📤 Enhanced ${reportType} export requested by ${user.full_name}`);
            showAlert(`Enhanced ${reportType} report export ready for implementation! 📊`, 'info');
        }

        function handleLogout() {
            if (confirm('Are you sure you want to logout?')) {
                authManager.logout();
                window.location.href = 'login.html';
            }
        }

        function showError(message) {
            showAlert(message, 'error');
        }

        function showAlert(message, type) {
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(alert, mainContent.firstChild);

            setTimeout(() => alert.remove(), 5000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeEnhancedReports);
    </script>
</body>
</html>