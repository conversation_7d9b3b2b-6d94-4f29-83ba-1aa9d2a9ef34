// Simplified Authentication Manager for Aanabi Pharmacy v2.0
// Optimized for performance and simplified user experience

class SimplifiedAuthManager {
    constructor() {
        this.currentUser = null;
        this.isInitialized = false;
        this.defaultUsers = this.getDefaultUsers();
        this.sessionKey = 'aanabi_current_mode';
        this.init();
    }

    async init() {
        console.log('🔐 Initializing simplified authentication system...');
        
        try {
            // Wait for database to be ready
            await this.waitForDatabase();
            
            // Load current session or default to staff
            await this.loadCurrentSession();
            
            this.isInitialized = true;
            console.log('✅ Simplified authentication system ready');
        } catch (error) {
            console.error('❌ Auth system initialization failed:', error);
            // Fallback to staff mode
            this.setStaffMode();
            this.isInitialized = true;
        }
    }

    async waitForDatabase() {
        let attempts = 0;
        const maxAttempts = 50; // Reduced attempts for faster startup

        while ((!window.dbManager || !window.dbManager.isReady()) && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.dbManager || !window.dbManager.isReady()) {
            console.warn('Database not available, using fallback authentication');
        }
    }

    // Simplified user definitions
    getDefaultUsers() {
        return {
            staff: {
                user_id: 'USER_STAFF',
                username: 'staff',
                full_name: 'Pharmacy Staff',
                role: 'staff',
                access_level: 4,
                permissions: {
                    dashboard: true,
                    medicines: true,
                    sales: true,
                    purchases: false,
                    customers: true,
                    suppliers: false,
                    inventory: true,
                    reports: true,
                    settings: false
                },
                status: 'Active'
            },
            admin: {
                user_id: 'USER_ADMIN',
                username: 'admin',
                full_name: 'System Administrator',
                role: 'admin',
                access_level: 10,
                permissions: {
                    dashboard: true,
                    medicines: true,
                    sales: true,
                    purchases: true,
                    customers: true,
                    suppliers: true,
                    inventory: true,
                    reports: true,
                    settings: true
                },
                status: 'Active'
            }
        };
    }

    // Load current session from localStorage or default to staff
    async loadCurrentSession() {
        try {
            const savedMode = localStorage.getItem(this.sessionKey);
            if (savedMode === 'admin') {
                this.currentUser = this.defaultUsers.admin;
            } else {
                this.currentUser = this.defaultUsers.staff;
            }
            
            console.log(`👤 Loaded session: ${this.currentUser.role} mode`);
        } catch (error) {
            console.warn('Could not load session, defaulting to staff mode');
            this.setStaffMode();
        }
    }

    // Switch to Staff Mode (Default - No password required)
    setStaffMode() {
        this.currentUser = this.defaultUsers.staff;
        this.saveSession('staff');
        this.updateUI();
        console.log('👤 Switched to Staff Mode');
        return { success: true, user: this.currentUser };
    }

    // Switch to Admin Mode (Password required)
    async switchToAdminMode(password = null) {
        try {
            // Simple password validation
            if (!password) {
                return { 
                    success: false, 
                    error: 'Password required for Admin mode',
                    needsPassword: true 
                };
            }

            if (password !== 'admin123') {
                return { 
                    success: false, 
                    error: 'Invalid password. Default password is "admin123"' 
                };
            }

            // Switch to admin mode
            this.currentUser = this.defaultUsers.admin;
            this.saveSession('admin');
            this.updateUI();
            
            console.log('👤 Switched to Admin Mode');
            return { 
                success: true, 
                user: this.currentUser,
                message: 'Welcome to Admin Mode! Full access enabled.' 
            };

        } catch (error) {
            console.error('Error switching to admin mode:', error);
            return { 
                success: false, 
                error: 'System error during mode switch' 
            };
        }
    }

    // Save current session
    saveSession(mode) {
        try {
            localStorage.setItem(this.sessionKey, mode);
        } catch (error) {
            console.warn('Could not save session:', error);
        }
    }

    // Update UI based on current user role
    updateUI() {
        // Update body class for role-based styling
        document.body.className = this.currentUser.role;
        
        // Update user info in header if elements exist
        this.updateUserInfo();
        
        // Trigger custom event for other components to listen
        window.dispatchEvent(new CustomEvent('authModeChanged', {
            detail: { user: this.currentUser }
        }));
    }

    updateUserInfo() {
        const userAvatar = document.getElementById('user-avatar');
        const userName = document.getElementById('user-name');
        const userRole = document.getElementById('user-role');
        const modeIndicator = document.getElementById('mode-indicator');
        
        if (userAvatar) {
            userAvatar.textContent = this.currentUser.role === 'admin' ? 'A' : 'S';
        }
        
        if (userName) {
            userName.textContent = this.currentUser.full_name;
        }
        
        if (userRole) {
            userRole.textContent = this.currentUser.role.charAt(0).toUpperCase() + this.currentUser.role.slice(1);
        }
        
        if (modeIndicator) {
            modeIndicator.className = `mode-indicator ${this.currentUser.role}`;
            modeIndicator.textContent = `${this.currentUser.role.charAt(0).toUpperCase() + this.currentUser.role.slice(1)} Mode`;
        }
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser || this.defaultUsers.staff;
    }

    // Check if current user has permission
    hasPermission(permission) {
        const user = this.getCurrentUser();
        return user.permissions[permission] === true;
    }

    // Check if user is admin
    isAdmin() {
        return this.getCurrentUser().role === 'admin';
    }

    // Check if user is staff
    isStaff() {
        return this.getCurrentUser().role === 'staff';
    }

    // Check if user is logged in (always true in simplified auth)
    isLoggedIn() {
        return this.isInitialized && this.currentUser !== null;
    }

    // Redirect to login (simplified - just show alert in this system)
    redirectToLogin(message = 'Please login to continue') {
        alert(message);
        // In a real system, this would redirect to login page
        // For now, just ensure user is in staff mode
        this.setStaffMode();
    }

    // Logout (simplified - just reset to staff mode)
    logout() {
        this.setStaffMode();
        localStorage.removeItem(this.sessionKey);
    }

    // Wait for auth system to be ready
    async waitForReady() {
        let attempts = 0;
        const maxAttempts = 50;

        while (!this.isInitialized && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!this.isInitialized) {
            console.warn('Auth system not ready, using fallback');
            this.setStaffMode();
            this.isInitialized = true;
        }
    }

    // Get user display info
    getUserDisplayInfo() {
        const user = this.getCurrentUser();
        return {
            name: user.full_name,
            role: user.role,
            avatar: user.role === 'admin' ? 'A' : 'S',
            modeText: `${user.role.charAt(0).toUpperCase() + user.role.slice(1)} Mode`,
            description: user.role === 'admin' 
                ? 'Full access mode. All features are available.'
                : 'Limited access mode. Go to Settings to switch to Admin mode for full access.'
        };
    }

    // Logout (reset to staff mode)
    logout() {
        this.setStaffMode();
        return { success: true, message: 'Logged out successfully' };
    }

    // Clear session data
    clearSession() {
        try {
            localStorage.removeItem(this.sessionKey);
        } catch (error) {
            console.warn('Could not clear session:', error);
        }
        this.setStaffMode();
    }

    // Get session info for debugging
    getSessionInfo() {
        return {
            currentUser: this.currentUser?.username || 'none',
            role: this.currentUser?.role || 'none',
            isInitialized: this.isInitialized,
            hasDatabase: !!(window.dbManager && window.dbManager.isReady())
        };
    }

    // Mode switching utilities for UI
    createModeSwitch() {
        const user = this.getCurrentUser();
        
        if (user.role === 'staff') {
            return {
                buttonText: '🔓 Switch to Admin Mode',
                buttonClass: 'btn btn-warning mode-switch-btn',
                onClick: () => this.promptAdminPassword()
            };
        } else {
            return {
                buttonText: '👤 Switch to Staff Mode',
                buttonClass: 'btn btn-primary mode-switch-btn admin-to-staff',
                onClick: () => this.setStaffMode()
            };
        }
    }

    // Prompt for admin password (can be called from UI)
    async promptAdminPassword() {
        const password = prompt('Enter Admin Password:');
        if (password === null) {
            return { success: false, error: 'Password entry cancelled' };
        }
        
        return await this.switchToAdminMode(password);
    }

    // Utility for creating permission-based UI elements
    filterMenuItems(menuItems) {
        return menuItems.filter(item => {
            if (!item.permission) return true;
            return this.hasPermission(item.permission);
        });
    }

    // Event listeners for mode switching
    setupModeSwithListeners() {
        // Listen for auth mode change events
        window.addEventListener('authModeChanged', (event) => {
            console.log('Auth mode changed:', event.detail.user.role);
        });
    }
}

// Utility functions for auth-related UI operations
const authUtils = {
    // Show elements based on permissions
    showIfPermitted(elementId, permission) {
        const element = document.getElementById(elementId);
        if (element && window.authManager) {
            element.style.display = authManager.hasPermission(permission) ? 'block' : 'none';
        }
    },

    // Hide elements based on permissions
    hideIfNotPermitted(elementId, permission) {
        const element = document.getElementById(elementId);
        if (element && window.authManager) {
            element.style.display = authManager.hasPermission(permission) ? 'none' : 'block';
        }
    },

    // Add permission-based classes
    applyPermissionClasses() {
        if (!window.authManager) return;
        
        const user = authManager.getCurrentUser();
        document.body.className = user.role;
        
        // Apply to specific elements
        document.querySelectorAll('.admin-only').forEach(el => {
            el.style.display = user.role === 'admin' ? 'block' : 'none';
        });
        
        document.querySelectorAll('.staff-only').forEach(el => {
            el.style.display = user.role === 'staff' ? 'block' : 'none';
        });
    },

    // Create mode switch button
    createModeButton(containerId) {
        if (!window.authManager) return;
        
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const switchInfo = authManager.createModeSwitch();
        const button = document.createElement('button');
        button.textContent = switchInfo.buttonText;
        button.className = switchInfo.buttonClass;
        button.onclick = switchInfo.onClick;
        
        container.innerHTML = '';
        container.appendChild(button);
    }
};

// Performance monitoring for auth system
const authPerformance = {
    startTime: Date.now(),
    
    getStats() {
        return {
            initTime: Date.now() - this.startTime,
            isReady: window.authManager?.isInitialized || false,
            currentMode: window.authManager?.getCurrentUser()?.role || 'unknown'
        };
    },
    
    logStats() {
        console.log('Auth Performance:', this.getStats());
    }
};

// Initialize simplified auth manager
const authManager = new SimplifiedAuthManager();

// Export utilities for global use
if (typeof window !== 'undefined') {
    window.authManager = authManager;
    window.authUtils = authUtils;
    window.authPerformance = authPerformance;
}

// Auto-setup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    authUtils.applyPermissionClasses();
    
    // Setup mode switch button if container exists
    authUtils.createModeButton('mode-switch-container');
});
