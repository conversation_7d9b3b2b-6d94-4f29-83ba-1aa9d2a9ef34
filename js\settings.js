// Settings management functionality

class SettingsManager {
    constructor() {
        this.settings = {};
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.updateDateTime();
        this.updateSystemInfo();
        setInterval(() => this.updateDateTime(), 1000);
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadSettings() {
        try {
            // Wait for database to be ready
            if (!dbManager.isReady()) {
                setTimeout(() => this.loadSettings(), 1000);
                return;
            }

            const settingsData = await dbManager.getAll('settings');

            // Convert array to object for easier access
            this.settings = {};
            settingsData.forEach(setting => {
                this.settings[setting.key] = setting.value;
            });

            this.populateSettingsForms();
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    populateSettingsForms() {
        // Pharmacy Information
        document.getElementById('pharmacy-name').value = this.settings.pharmacy_name || 'Aanabi Pharmacy';
        document.getElementById('license-number').value = this.settings.license_number || 'PH-2024-001';
        document.getElementById('address').value = this.settings.address || 'Main Street, Kathmandu, Nepal';
        document.getElementById('phone').value = this.settings.phone || '01-4567890';
        document.getElementById('email').value = this.settings.email || '<EMAIL>';
        document.getElementById('gst-number').value = this.settings.gst_number || '*********';

        // ✨ Enhanced Tax & Pricing (VAT/PAN System)
        document.getElementById('tax-system').value = this.settings.tax_system || 'vat';
        document.getElementById('vat-rate').value = this.settings.vat_rate || '13';
        document.getElementById('vat-registration-number').value = this.settings.vat_registration_number || 'VAT-*********';
        document.getElementById('pan-number').value = this.settings.pan_number || 'PAN-*********';
        document.getElementById('default-margin').value = this.settings.default_margin || '60';
        document.getElementById('loyalty-points-rate').value = this.settings.loyalty_points_rate || '20';
        document.getElementById('loyalty-redemption-rate').value = this.settings.loyalty_redemption_rate || '0.10';
        document.getElementById('currency-symbol').value = this.settings.currency_symbol || 'Rs.';
        
        // Update tax system display
        this.updateTaxSystemDisplay(this.settings.tax_system || 'vat');

        // Alert Settings
        document.getElementById('low-stock-days').value = this.settings.low_stock_alert_days || '30';
        document.getElementById('expiry-alert-days').value = this.settings.expiry_alert_days || '30';
        document.getElementById('payment-due-days').value = this.settings.payment_due_alert_days || '7';
        document.getElementById('email-alerts').value = this.settings.email_alerts || 'enabled';

        // System Preferences
        document.getElementById('date-format').value = this.settings.date_format || 'MM/DD/YYYY';
        document.getElementById('time-format').value = this.settings.time_format || '12';
        document.getElementById('backup-frequency').value = this.settings.backup_frequency || 'weekly';
        document.getElementById('theme').value = this.settings.theme || 'light';
    }

    setupEventListeners() {
        // Pharmacy Information Form
        const pharmacyForm = document.getElementById('pharmacy-info-form');
        if (pharmacyForm) {
            pharmacyForm.addEventListener('submit', (e) => this.handlePharmacyInfoSubmit(e));
        }

        // Tax & Pricing Form
        const taxForm = document.getElementById('tax-pricing-form');
        if (taxForm) {
            taxForm.addEventListener('submit', (e) => this.handleTaxPricingSubmit(e));
        }

        // Alert Settings Form
        const alertForm = document.getElementById('alert-settings-form');
        if (alertForm) {
            alertForm.addEventListener('submit', (e) => this.handleAlertSettingsSubmit(e));
        }

        // System Preferences Form
        const preferencesForm = document.getElementById('system-preferences-form');
        if (preferencesForm) {
            preferencesForm.addEventListener('submit', (e) => this.handleSystemPreferencesSubmit(e));
        }

        // Import Form
        const importForm = document.getElementById('import-form');
        if (importForm) {
            importForm.addEventListener('submit', (e) => this.handleImportSubmit(e));
        }

        // Clear Data Form
        const clearDataForm = document.getElementById('clear-data-form');
        if (clearDataForm) {
            clearDataForm.addEventListener('submit', (e) => this.handleClearDataSubmit(e));
        }

        // ✨ Enhanced Tax System Toggle
        const taxSystemSelect = document.getElementById('tax-system');
        if (taxSystemSelect) {
            taxSystemSelect.addEventListener('change', (e) => this.handleTaxSystemChange(e));
        }
    }

    async handlePharmacyInfoSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const settings = Object.fromEntries(formData.entries());

        try {
            for (const [key, value] of Object.entries(settings)) {
                await this.saveSetting(key, value);
            }
            
            this.showAlert('Pharmacy information updated successfully!', 'success');
        } catch (error) {
            console.error('Error saving pharmacy info:', error);
            this.showAlert('Error saving pharmacy information. Please try again.', 'danger');
        }
    }

    async handleTaxPricingSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const settings = Object.fromEntries(formData.entries());

        try {
            for (const [key, value] of Object.entries(settings)) {
                await this.saveSetting(key, value);
            }
            
            // Update tax system display
            if (settings['tax-system']) {
                this.updateTaxSystemDisplay(settings['tax-system']);
            }
            
            this.showAlert('Tax and pricing settings updated successfully! ✨ New tax system is now active.', 'success');
        } catch (error) {
            console.error('Error saving tax settings:', error);
            this.showAlert('Error saving tax settings. Please try again.', 'danger');
        }
    }

    // ✨ NEW: Enhanced Tax System Management
    handleTaxSystemChange(event) {
        const selectedSystem = event.target.value;
        this.updateTaxSystemDisplay(selectedSystem);
        
        // Show confirmation message
        const systemName = selectedSystem === 'vat' ? 'VAT (13% tax)' : 'PAN (0% tax)';
        this.showAlert(`Tax system changed to ${systemName}. Don't forget to save your settings!`, 'info');
    }

    updateTaxSystemDisplay(taxSystem) {
        const vatFields = document.getElementById('vat-fields');
        const panFields = document.getElementById('pan-fields');
        const taxSystemStatus = document.getElementById('tax-system-status');
        
        if (vatFields && panFields) {
            if (taxSystem === 'vat') {
                vatFields.style.display = 'block';
                panFields.style.display = 'none';
                if (taxSystemStatus) {
                    taxSystemStatus.innerHTML = `
                        <span class="badge badge-success">
                            📊 VAT System Active (13% Tax)
                        </span>
                    `;
                }
            } else {
                vatFields.style.display = 'none';
                panFields.style.display = 'block';
                if (taxSystemStatus) {
                    taxSystemStatus.innerHTML = `
                        <span class="badge badge-info">
                            📄 PAN System Active (0% Tax)
                        </span>
                    `;
                }
            }
        }
    }

    async handleAlertSettingsSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const settings = Object.fromEntries(formData.entries());

        try {
            for (const [key, value] of Object.entries(settings)) {
                await this.saveSetting(key, value);
            }
            
            this.showAlert('Alert settings updated successfully!', 'success');
        } catch (error) {
            console.error('Error saving alert settings:', error);
            this.showAlert('Error saving alert settings. Please try again.', 'danger');
        }
    }

    async handleSystemPreferencesSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const settings = Object.fromEntries(formData.entries());

        try {
            for (const [key, value] of Object.entries(settings)) {
                await this.saveSetting(key, value);
            }
            
            this.showAlert('System preferences updated successfully!', 'success');
            
            // Apply theme change immediately
            if (settings.theme) {
                this.applyTheme(settings.theme);
            }
        } catch (error) {
            console.error('Error saving preferences:', error);
            this.showAlert('Error saving preferences. Please try again.', 'danger');
        }
    }

    async handleImportSubmit(event) {
        event.preventDefault();
        
        const importType = document.getElementById('import-type').value;
        const fileInput = document.getElementById('import-file');
        const file = fileInput.files[0];

        if (!file) {
            this.showAlert('Please select a CSV file to import.', 'warning');
            return;
        }

        try {
            const csvText = await this.readFileAsText(file);
            const data = this.parseCSV(csvText);
            
            await this.importData(importType, data);
            
            this.showAlert(`${importType} data imported successfully!`, 'success');
            this.closeImportModal();
        } catch (error) {
            console.error('Error importing data:', error);
            this.showAlert('Error importing data. Please check your CSV format.', 'danger');
        }
    }

    async handleClearDataSubmit(event) {
        event.preventDefault();
        
        const confirmText = document.getElementById('confirm-text').value;
        if (confirmText !== 'CONFIRM') {
            this.showAlert('Please type "CONFIRM" to proceed with data clearing.', 'warning');
            return;
        }

        const formData = new FormData(event.target);
        const selectedData = [];
        
        for (const [key, value] of formData.entries()) {
            if (key.startsWith('clear_')) {
                selectedData.push(value);
            }
        }

        if (selectedData.length === 0) {
            this.showAlert('Please select at least one data type to clear.', 'warning');
            return;
        }

        try {
            if (selectedData.includes('all')) {
                await this.clearAllData();
            } else {
                await this.clearSelectedData(selectedData);
            }
            
            this.showAlert('Selected data cleared successfully!', 'success');
            this.closeClearDataModal();
        } catch (error) {
            console.error('Error clearing data:', error);
            this.showAlert('Error clearing data. Please try again.', 'danger');
        }
    }

    async saveSetting(key, value) {
        const settingData = { key, value };
        
        try {
            // Check if setting exists
            const existingSetting = await dbManager.get('settings', key);
            if (existingSetting) {
                await dbManager.update('settings', settingData);
            } else {
                await dbManager.insert('settings', settingData);
            }
            
            this.settings[key] = value;
        } catch (error) {
            console.error('Error saving setting:', error);
            throw error;
        }
    }

    async updateSystemInfo() {
        try {
            // Calculate database size and record counts
            const [sales, medicines, customers, suppliers, purchases, inventory, users, auditLogs, notifications] = await Promise.all([
                dbManager.getAll('sales'),
                dbManager.getAll('medicines'),
                dbManager.getAll('customers'),
                dbManager.getAll('suppliers'),
                dbManager.getAll('purchases'),
                dbManager.getAll('inventory'),
                dbManager.getAll('users'),
                dbManager.getAll('audit_logs'),
                dbManager.getAll('notifications')
            ]);

            const totalRecords = sales.length + medicines.length + customers.length + 
                               suppliers.length + purchases.length + inventory.length +
                               users.length + auditLogs.length + notifications.length;

            document.getElementById('total-records').textContent = totalRecords.toLocaleString();
            
            // ✨ Enhanced System Statistics
            const enhancedStats = {
                totalUsers: users.length,
                auditLogs: auditLogs.length,
                activeNotifications: notifications.filter(n => n.status === 'Active').length,
                bonusStockItems: medicines.filter(m => (m.bonus_stock || 0) > 0).length,
                totalLoyaltyPoints: customers.reduce((sum, c) => sum + (c.loyalty_points || 0), 0)
            };
            
            this.displayEnhancedStats(enhancedStats);
            document.getElementById('database-size').textContent = 'Enhanced DB v2.0';

            // Load recent activity
            this.loadRecentActivity(sales, purchases);

        } catch (error) {
            console.error('Error updating system info:', error);
        }
    }

    displayEnhancedStats(stats) {
        const statsContainer = document.getElementById('enhanced-stats');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <strong>📍 ${stats.totalUsers}</strong><br>
                            <small>System Users</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <strong>📝 ${stats.auditLogs}</strong><br>
                            <small>Audit Logs</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <strong>🔔 ${stats.activeNotifications}</strong><br>
                            <small>Active Alerts</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <strong>🎁 ${stats.bonusStockItems}</strong><br>
                            <small>Bonus Stock Items</small>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="stat-item">
                            <strong>🏆 ${stats.totalLoyaltyPoints.toLocaleString()}</strong><br>
                            <small>Total Loyalty Points</small>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    loadRecentActivity(sales, purchases) {
        const recentActivity = [];
        
        // Add recent sales
        sales.slice(-5).forEach(sale => {
            recentActivity.push({
                type: 'Sale',
                description: `Sale ${sale.sale_id} - ${sale.medicine_name}`,
                date: new Date(sale.date),
                icon: '💳'
            });
        });

        // Add recent purchases
        purchases.slice(-5).forEach(purchase => {
            recentActivity.push({
                type: 'Purchase',
                description: `Purchase ${purchase.purchase_id} - ${purchase.medicine_name}`,
                date: new Date(purchase.date),
                icon: '🛒'
            });
        });

        // Sort by date (newest first)
        recentActivity.sort((a, b) => b.date - a.date);

        const activityContainer = document.getElementById('recent-activity');
        activityContainer.innerHTML = '';

        if (recentActivity.length === 0) {
            activityContainer.innerHTML = '<p class="text-muted">No recent activity found.</p>';
            return;
        }

        recentActivity.slice(0, 8).forEach(activity => {
            const activityElement = document.createElement('div');
            activityElement.className = 'mb-2';
            activityElement.innerHTML = `
                <small class="text-muted">${activity.date.toLocaleDateString()}</small><br>
                ${activity.icon} ${activity.description}
            `;
            activityContainer.appendChild(activityElement);
        });
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    parseCSV(csvText) {
        const lines = csvText.split('\n');
        const headers = lines[0].split(',').map(h => h.trim());
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = lines[i].split(',');
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index] ? values[index].trim().replace(/"/g, '') : '';
                });
                data.push(row);
            }
        }

        return data;
    }

    async importData(type, data) {
        switch (type) {
            case 'medicines':
                await this.importMedicines(data);
                break;
            case 'customers':
                await this.importCustomers(data);
                break;
            case 'suppliers':
                await this.importSuppliers(data);
                break;
            default:
                throw new Error('Unsupported import type');
        }
    }

    async importMedicines(data) {
        for (const row of data) {
            const medicine = {
                medicine_id: await dbManager.generateId('medicines', 'MED'),
                medicine_name: row.medicine_name || row.name,
                generic_name: row.generic_name || '',
                category: row.category || 'Others',
                manufacturer: row.manufacturer || '',
                pack_size: row.pack_size || '',
                unit_type: row.unit_type || 'Tablets',
                min_stock_level: parseInt(row.min_stock_level) || 10,
                current_stock: parseInt(row.current_stock) || 0,
                purchase_price: parseFloat(row.purchase_price) || 0,
                selling_price: parseFloat(row.selling_price) || 0,
                margin_percent: parseFloat(row.margin_percent) || 0,
                storage_location: row.storage_location || '',
                prescription_required: row.prescription_required || 'No',
                status: row.status || 'Active'
            };
            
            await dbManager.insert('medicines', medicine);
        }
    }

    async importCustomers(data) {
        for (const row of data) {
            const customer = {
                customer_id: await dbManager.generateId('customers', 'CUST'),
                customer_name: row.customer_name || row.name,
                phone_number: row.phone_number || row.phone,
                email: row.email || '',
                date_of_birth: row.date_of_birth || '',
                address: row.address || '',
                medical_conditions: row.medical_conditions || '',
                allergies: row.allergies || '',
                registration_date: new Date().toISOString().split('T')[0],
                total_purchase_amount: parseFloat(row.total_purchase_amount) || 0,
                loyalty_points: parseInt(row.loyalty_points) || 0,
                status: row.status || 'Active'
            };
            
            await dbManager.insert('customers', customer);
        }
    }

    async importSuppliers(data) {
        for (const row of data) {
            const supplier = {
                supplier_id: await dbManager.generateId('suppliers', 'SUP'),
                supplier_name: row.supplier_name || row.name,
                contact_person: row.contact_person || '',
                phone_number: row.phone_number || row.phone,
                email: row.email || '',
                gst_number: row.gst_number || '',
                address: row.address || '',
                payment_terms: row.payment_terms || 'Net 30',
                credit_limit: parseFloat(row.credit_limit) || 0,
                outstanding_amount: parseFloat(row.outstanding_amount) || 0,
                total_purchase_amount: parseFloat(row.total_purchase_amount) || 0,
                status: row.status || 'Active'
            };
            
            await dbManager.insert('suppliers', supplier);
        }
    }

    async clearSelectedData(dataTypes) {
        for (const dataType of dataTypes) {
            switch (dataType) {
                case 'sales':
                    await this.clearTableData('sales');
                    break;
                case 'purchases':
                    await this.clearTableData('purchases');
                    break;
                case 'customers':
                    await this.clearTableData('customers');
                    break;
                case 'inventory':
                    await this.clearTableData('inventory');
                    break;
            }
        }
    }

    async clearAllData() {
        const tables = ['sales', 'purchases', 'customers', 'suppliers', 'medicines', 'inventory'];
        for (const table of tables) {
            await this.clearTableData(table);
        }
    }

    async clearTableData(tableName) {
        const records = await dbManager.getAll(tableName);
        for (const record of records) {
            const key = Object.keys(record)[0]; // Get the primary key
            await dbManager.delete(tableName, record[key]);
        }
    }

    applyTheme(theme) {
        // Enhanced theme implementation
        if (theme === 'dark') {
            document.body.classList.add('dark-theme');
            this.showAlert('🌙 Dark theme applied!', 'info');
        } else {
            document.body.classList.remove('dark-theme');
            this.showAlert('☀️ Light theme applied!', 'info');
        }
    }

    // ✨ NEW: Enhanced Backup with all enhanced data
    async createEnhancedBackup() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                version: '2.0.0-enhanced',
                systemInfo: {
                    taxSystem: this.settings.tax_system || 'vat',
                    loyaltyEnabled: true,
                    bonusStockEnabled: true,
                    auditEnabled: true
                },
                data: {
                    medicines: await dbManager.getAll('medicines'),
                    customers: await dbManager.getAll('customers'),
                    sales: await dbManager.getAll('sales'),
                    purchases: await dbManager.getAll('purchases'),
                    suppliers: await dbManager.getAll('suppliers'),
                    inventory: await dbManager.getAll('inventory'),
                    users: await dbManager.getAll('users'),
                    settings: await dbManager.getAll('settings'),
                    audit_logs: await dbManager.getAll('audit_logs'),
                    notifications: await dbManager.getAll('notifications')
                }
            };
            
            const backupJson = JSON.stringify(backupData, null, 2);
            const blob = new Blob([backupJson], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `aanabi_enhanced_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            await this.saveSetting('last_backup_date', new Date().toISOString());
            this.showAlert('✨ Enhanced backup created successfully with all system data!', 'success');
            
            // Update last backup date display
            document.getElementById('last-backup').textContent = new Date().toLocaleDateString();
            
            return true;
        } catch (error) {
            console.error('Error creating enhanced backup:', error);
            this.showAlert('Error creating backup. Please try again.', 'danger');
            return false;
        }
    }

    showAlert(message, type) {
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        setTimeout(() => alert.remove(), 3000);
    }

    closeImportModal() {
        document.getElementById('import-modal').style.display = 'none';
        document.getElementById('import-form').reset();
    }

    closeClearDataModal() {
        document.getElementById('clear-data-modal').style.display = 'none';
        document.getElementById('clear-data-form').reset();
        document.getElementById('confirm-text').value = '';
    }
}

// Global functions
function showImportModal() {
    document.getElementById('import-modal').style.display = 'flex';
}

function closeImportModal() {
    settingsManager.closeImportModal();
}

function showClearDataModal() {
    document.getElementById('clear-data-modal').style.display = 'flex';
}

function closeClearDataModal() {
    settingsManager.closeClearDataModal();
}

function exportAllData() {
    try {
        // This would create a comprehensive backup of all data
        alert('This feature would export all system data. Implementation would depend on specific requirements.');
        settingsManager.showAlert('Export functionality is ready for implementation!', 'info');
    } catch (error) {
        console.error('Error exporting data:', error);
        settingsManager.showAlert('Error exporting data. Please try again.', 'danger');
    }
}

// ✨ Enhanced Backup Function
function createBackup() {
    if (settingsManager && settingsManager.createEnhancedBackup) {
        settingsManager.createEnhancedBackup();
    } else {
        // Fallback to basic backup
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                data: {
                    // This would include all system data
                }
            };
            
            const backupJson = JSON.stringify(backupData, null, 2);
            const blob = new Blob([backupJson], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `aanabi_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            if (settingsManager) {
                settingsManager.showAlert('Backup created successfully!', 'success');
            }
            
            // Update last backup date
            const lastBackupElement = document.getElementById('last-backup');
            if (lastBackupElement) {
                lastBackupElement.textContent = new Date().toLocaleDateString();
            }
        } catch (error) {
            console.error('Error creating backup:', error);
            if (settingsManager) {
                settingsManager.showAlert('Error creating backup. Please try again.', 'danger');
            }
        }
    }
}

// Initialize settings manager when page loads
let settingsManager;
document.addEventListener('DOMContentLoaded', () => {
    settingsManager = new SettingsManager();
});