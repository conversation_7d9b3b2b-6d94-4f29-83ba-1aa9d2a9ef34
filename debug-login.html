<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login - Aanabi Pharmacy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        input, button {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            width: auto;
            padding: 10px 20px;
        }
        button:hover {
            background: #0056b3;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Aanabi Login Debug Tool</h1>
        
        <div id="system-status" class="status info">
            🔄 Initializing systems...
        </div>

        <h3>Database Status</h3>
        <div id="db-status" class="status warning">Checking database...</div>
        <button onclick="checkDatabase()">🔍 Check Database</button>
        <button onclick="listUsers()">👥 List Users</button>
        <button onclick="resetDatabase()">🔄 Reset Database</button>

        <h3>Authentication Test</h3>
        <div>
            <input type="text" id="test-username" placeholder="Username" value="admin">
            <input type="password" id="test-password" placeholder="Password" value="admin123">
            <button onclick="testLogin()">🔐 Test Login</button>
        </div>
        
        <div id="login-result" class="status" style="display: none;"></div>

        <h3>Quick Actions</h3>
        <button onclick="createTestUsers()">👤 Create Test Users</button>
        <button onclick="clearStorage()">🗑️ Clear Browser Storage</button>
        <button onclick="goToLogin()">➡️ Go to Login Page</button>

        <h3>Debug Log</h3>
        <div id="debug-log" class="debug-log">
            Debug information will appear here...
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    
    <script>
        let debugLog = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            debugLog.push(logMessage);
            document.getElementById('debug-log').textContent = debugLog.join('\n');
            console.log(logMessage);
        }

        async function init() {
            log('🚀 Starting debug initialization...');
            
            // Wait for systems
            let attempts = 0;
            while ((!window.dbManager || !window.authManager) && attempts < 100) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
                if (attempts % 10 === 0) {
                    log(`⏳ Waiting for systems... (${attempts/10}s)`);
                }
            }

            if (!window.dbManager) {
                document.getElementById('system-status').className = 'status error';
                document.getElementById('system-status').textContent = '❌ Database Manager not found';
                log('❌ Database Manager failed to initialize');
                return;
            }

            if (!window.authManager) {
                document.getElementById('system-status').className = 'status error';
                document.getElementById('system-status').textContent = '❌ Auth Manager not found';
                log('❌ Auth Manager failed to initialize');
                return;
            }

            log('✅ Managers found, waiting for database to be ready...');
            
            // Wait for database to be ready
            attempts = 0;
            while (!dbManager.isReady() && attempts < 100) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
                if (attempts % 10 === 0) {
                    log(`⏳ Waiting for database... (${attempts/10}s)`);
                }
            }

            if (dbManager.isReady()) {
                document.getElementById('system-status').className = 'status success';
                document.getElementById('system-status').textContent = '✅ All systems ready!';
                log('✅ Database is ready');
                await checkDatabase();
            } else {
                document.getElementById('system-status').className = 'status error';
                document.getElementById('system-status').textContent = '❌ Database failed to initialize';
                log('❌ Database failed to be ready');
            }
        }

        async function checkDatabase() {
            try {
                log('🔍 Checking database structure...');
                
                if (!dbManager.isReady()) {
                    throw new Error('Database not ready');
                }

                const users = await dbManager.getAll('users');
                log(`👥 Found ${users.length} users in database`);
                
                if (users.length === 0) {
                    document.getElementById('db-status').className = 'status warning';
                    document.getElementById('db-status').textContent = '⚠️ No users found - database may need initialization';
                    log('⚠️ No users found in database');
                } else {
                    document.getElementById('db-status').className = 'status success';
                    document.getElementById('db-status').textContent = `✅ Database ready with ${users.length} users`;
                    
                    users.forEach(user => {
                        log(`👤 User: ${user.username} (${user.role}) - ${user.status}`);
                    });
                }

                // Check settings
                const settings = await dbManager.getAll('settings');
                log(`⚙️ Found ${settings.length} settings`);

            } catch (error) {
                document.getElementById('db-status').className = 'status error';
                document.getElementById('db-status').textContent = '❌ Database error: ' + error.message;
                log('❌ Database check failed: ' + error.message);
            }
        }

        async function listUsers() {
            try {
                log('📋 Listing all users...');
                const users = await dbManager.getAll('users');
                
                if (users.length === 0) {
                    log('📋 No users found');
                    return;
                }

                users.forEach(user => {
                    log(`👤 ${user.username}:`);
                    log(`   - ID: ${user.user_id}`);
                    log(`   - Role: ${user.role}`);
                    log(`   - Status: ${user.status}`);
                    log(`   - Locked: ${user.account_locked || false}`);
                    log(`   - Attempts: ${user.login_attempts || 0}`);
                    log(`   - Hash: ${user.password_hash.substring(0, 20)}...`);
                });

            } catch (error) {
                log('❌ Failed to list users: ' + error.message);
            }
        }

        async function testLogin() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            log(`🔐 Testing login for: ${username}`);
            
            try {
                // Test password verification directly
                const users = await dbManager.search('users', 'username', username);
                if (users.length === 0) {
                    throw new Error('User not found');
                }

                const user = users[0];
                log(`👤 Found user: ${user.username}`);
                log(`🔒 User hash: ${user.password_hash}`);
                log(`🔑 Testing password: ${password}`);

                // Test hash verification
                const simpleHashes = {
                    'admin123': '$2b$10$8K1p/a9yl4gVWLGPdNPl8OY4tOQ8UY5T8E8K1p/a9yl4gVWLGPdNPl8O',
                    'staff123': '$2b$10$9K2q/b0zm5hXWMHQeOQm9PZ5uPR9VZ6U9F9K2q/b0zm5hXWMHQeOQm9P'
                };
                
                const expectedHash = simpleHashes[password];
                log(`🔐 Expected hash: ${expectedHash}`);
                log(`🔍 Hash match: ${expectedHash === user.password_hash}`);

                // Now test full login
                const result = await authManager.login(username, password);
                
                if (result.success) {
                    document.getElementById('login-result').className = 'status success';
                    document.getElementById('login-result').textContent = `✅ Login successful! Welcome ${result.user.full_name}`;
                    log('✅ Login test successful');
                } else {
                    document.getElementById('login-result').className = 'status error';
                    document.getElementById('login-result').textContent = `❌ Login failed: ${result.error}`;
                    log('❌ Login test failed: ' + result.error);
                }
                
                document.getElementById('login-result').style.display = 'block';

            } catch (error) {
                document.getElementById('login-result').className = 'status error';
                document.getElementById('login-result').textContent = `❌ Login test error: ${error.message}`;
                document.getElementById('login-result').style.display = 'block';
                log('❌ Login test error: ' + error.message);
            }
        }

        async function createTestUsers() {
            try {
                log('👤 Creating test users...');
                
                // Clear existing users first
                const existingUsers = await dbManager.getAll('users');
                for (const user of existingUsers) {
                    await dbManager.delete('users', user.user_id);
                }
                log('🗑️ Cleared existing users');

                // Create fresh users
                await dbManager.insertSampleUsers();
                log('✅ Sample users created');
                
                await checkDatabase();

            } catch (error) {
                log('❌ Failed to create test users: ' + error.message);
            }
        }

        async function resetDatabase() {
            if (confirm('Are you sure you want to reset the entire database? This will delete all data!')) {
                try {
                    log('🔄 Resetting database...');
                    await dbManager.clearDatabase();
                    log('✅ Database cleared');
                    
                    // Reload the page to reinitialize
                    window.location.reload();
                } catch (error) {
                    log('❌ Failed to reset database: ' + error.message);
                }
            }
        }

        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            log('🗑️ Browser storage cleared');
        }

        function goToLogin() {
            window.location.href = 'pages/login.html';
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>