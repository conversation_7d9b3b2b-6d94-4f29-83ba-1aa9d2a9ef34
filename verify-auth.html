<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Verification - Aanabi Pharmacy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .step h3 { margin-top: 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Authentication Verification Tool</h1>
        <p>This tool verifies that the authentication system is working correctly.</p>
        
        <div id="overall-status" class="status info">🔄 Starting verification...</div>
        
        <div class="step">
            <h3>Step 1: System Initialization</h3>
            <div id="step1-status" class="status warning">⏳ Waiting...</div>
            <p id="step1-details">Checking if database and auth managers initialize correctly...</p>
        </div>
        
        <div class="step">
            <h3>Step 2: Database Content</h3>
            <div id="step2-status" class="status warning">⏳ Waiting...</div>
            <p id="step2-details">Verifying users exist in database...</p>
        </div>
        
        <div class="step">
            <h3>Step 3: Authentication Test</h3>
            <div id="step3-status" class="status warning">⏳ Waiting...</div>
            <p id="step3-details">Testing login with admin credentials...</p>
        </div>
        
        <div class="step">
            <h3>Step 4: Session Management</h3>
            <div id="step4-status" class="status warning">⏳ Waiting...</div>
            <p id="step4-details">Testing session creation and recovery...</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="runVerification()">🔄 Run Verification</button>
            <button onclick="goToDashboard()">➡️ Go to Dashboard</button>
            <button onclick="goToLogin()">🔐 Go to Login</button>
        </div>
        
        <div id="debug-log" style="background: #f8f9fa; padding: 15px; margin-top: 20px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; display: none;">
        </div>
        
        <button onclick="toggleDebugLog()" style="margin-top: 10px;">🐛 Toggle Debug Log</button>
    </div>

    <!-- Scripts -->
    <script src="js/database.js"></script>
    <script src="js/auth.js"></script>
    
    <script>
        let logs = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logs.push(logMessage);
            document.getElementById('debug-log').textContent = logs.join('\n');
            console.log(logMessage);
        }
        
        function updateStep(step, status, message, details = '') {
            const statusEl = document.getElementById(`step${step}-status`);
            const detailsEl = document.getElementById(`step${step}-details`);
            
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
            if (details) detailsEl.textContent = details;
        }
        
        async function runVerification() {
            log('🚀 Starting authentication verification...');
            
            try {
                // Step 1: Wait for systems
                updateStep(1, 'info', '🔄 Initializing systems...');
                await waitForSystems();
                updateStep(1, 'success', '✅ Systems initialized', 'Database and auth managers are ready');
                log('✅ Step 1 passed: Systems initialized');
                
                // Step 2: Check database
                updateStep(2, 'info', '🔄 Checking database...');
                await verifyDatabase();
                updateStep(2, 'success', '✅ Database verified', 'Users found and accessible');
                log('✅ Step 2 passed: Database verified');
                
                // Step 3: Test authentication
                updateStep(3, 'info', '🔄 Testing authentication...');
                await testAuthentication();
                updateStep(3, 'success', '✅ Authentication working', 'Login test successful');
                log('✅ Step 3 passed: Authentication working');
                
                // Step 4: Test session management
                updateStep(4, 'info', '🔄 Testing session management...');
                await testSessionManagement();
                updateStep(4, 'success', '✅ Session management working', 'Session creation and recovery successful');
                log('✅ Step 4 passed: Session management working');
                
                // Overall success
                document.getElementById('overall-status').className = 'status success';
                document.getElementById('overall-status').textContent = '🎉 All verification tests passed! Authentication system is working correctly.';
                log('🎉 All verification tests passed!');
                
            } catch (error) {
                log(`❌ Verification failed: ${error.message}`);
                document.getElementById('overall-status').className = 'status error';
                document.getElementById('overall-status').textContent = `❌ Verification failed: ${error.message}`;
            }
        }
        
        async function waitForSystems() {
            let attempts = 0;
            const maxAttempts = 100;
            
            while ((!window.dbManager || !window.authManager) && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!window.dbManager) throw new Error('Database manager not found');
            if (!window.authManager) throw new Error('Auth manager not found');
            
            log('📊 Managers found, waiting for database...');
            
            attempts = 0;
            while (!dbManager.isReady() && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!dbManager.isReady()) throw new Error('Database failed to initialize');
            
            log('🔐 Waiting for auth initialization...');
            await authManager.waitForDatabase();
        }
        
        async function verifyDatabase() {
            const users = await dbManager.getAll('users');
            log(`👥 Found ${users.length} users`);
            
            if (users.length === 0) {
                throw new Error('No users found in database');
            }
            
            const adminUser = users.find(u => u.username === 'admin');
            const staffUser = users.find(u => u.username === 'staff');
            
            if (!adminUser) throw new Error('Admin user not found');
            if (!staffUser) throw new Error('Staff user not found');
            
            log(`👤 Admin user: ${adminUser.username} (${adminUser.role})`);
            log(`👤 Staff user: ${staffUser.username} (${staffUser.role})`);
        }
        
        async function testAuthentication() {
            // Clear any existing session
            localStorage.removeItem('aanabi_session');
            authManager.currentUser = null;
            
            log('🔐 Testing login with admin credentials...');
            const result = await authManager.login('admin', 'admin123');
            
            if (!result.success) {
                throw new Error(`Login failed: ${result.error}`);
            }
            
            if (!authManager.isLoggedIn()) {
                throw new Error('User not marked as logged in after successful login');
            }
            
            log(`✅ Login successful for ${result.user.full_name}`);
        }
        
        async function testSessionManagement() {
            // Verify session was created
            const sessionData = localStorage.getItem('aanabi_session');
            if (!sessionData) {
                throw new Error('Session not created in localStorage');
            }
            
            log('📝 Session created in localStorage');
            
            // Test session recovery
            const currentUser = authManager.getCurrentUser();
            if (!currentUser) {
                throw new Error('No current user after login');
            }
            
            // Simulate page reload by clearing current user and recovering session
            authManager.currentUser = null;
            const recovered = await authManager.checkExistingSession();
            
            if (!recovered) {
                throw new Error('Session recovery failed');
            }
            
            if (!authManager.isLoggedIn()) {
                throw new Error('User not logged in after session recovery');
            }
            
            log('🔄 Session recovery successful');
        }
        
        function toggleDebugLog() {
            const logEl = document.getElementById('debug-log');
            logEl.style.display = logEl.style.display === 'none' ? 'block' : 'none';
        }
        
        function goToDashboard() {
            window.location.href = 'index.html';
        }
        
        function goToLogin() {
            window.location.href = 'pages/login.html';
        }
        
        // Auto-run verification when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runVerification, 1000);
        });
    </script>
</body>
</html>