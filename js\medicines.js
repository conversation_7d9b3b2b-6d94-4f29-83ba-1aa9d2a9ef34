// Medicine management functionality

class MedicineManager {
    constructor() {
        this.medicines = [];
        this.filteredMedicines = [];
        this.init();
    }

    async init() {
        // Wait for database to be ready
        await this.waitForDatabase();
        await this.loadMedicines();
        this.setupEventListeners();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    }

    // Wait for database to be ready
    async waitForDatabase() {
        let attempts = 0;
        const maxAttempts = 100;

        while ((!window.dbManager?.isReady()) && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.dbManager?.isReady()) {
            throw new Error('Database failed to initialize');
        }
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('medicine-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterMedicines(e.target.value));
        }

        // Form submission
        const form = document.getElementById('medicine-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Price calculation
        const purchasePrice = document.getElementById('purchase-price');
        const sellingPrice = document.getElementById('selling-price');
        if (purchasePrice && sellingPrice) {
            purchasePrice.addEventListener('input', () => this.calculateMargin());
            sellingPrice.addEventListener('input', () => this.calculateMargin());
        }
    }

    async loadMedicines() {
        try {
            // Wait for database to be ready
            if (!dbManager.isReady()) {
                setTimeout(() => this.loadMedicines(), 1000);
                return;
            }

            this.medicines = await dbManager.getAll('medicines');
            this.filteredMedicines = [...this.medicines];
            this.renderMedicines();
            this.updateStatistics();
        } catch (error) {
            console.error('Error loading medicines:', error);
        }
    }

    filterMedicines(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredMedicines = this.medicines.filter(medicine => 
            medicine.medicine_name.toLowerCase().includes(term) ||
            medicine.generic_name.toLowerCase().includes(term) ||
            medicine.category.toLowerCase().includes(term) ||
            medicine.manufacturer.toLowerCase().includes(term) ||
            medicine.medicine_id.toLowerCase().includes(term)
        );
        this.renderMedicines();
    }

    renderMedicines() {
        const tbody = document.getElementById('medicines-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredMedicines.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" class="text-center text-muted">No medicines found</td></tr>';
            return;
        }

        this.filteredMedicines.forEach(medicine => {
            const row = document.createElement('tr');
            
            // Add row class based on stock level
            if (medicine.current_stock <= medicine.min_stock_level) {
                row.classList.add('table-warning');
            }

            row.innerHTML = `
                <td>${medicine.medicine_id}</td>
                <td>${medicine.medicine_name}</td>
                <td>${medicine.generic_name || '-'}</td>
                <td>${medicine.category}</td>
                <td>${medicine.manufacturer}</td>
                <td>
                    <span class="${medicine.current_stock <= medicine.min_stock_level ? 'text-danger' : 'text-success'}">
                        ${medicine.current_stock}
                    </span>
                </td>
                <td>${medicine.min_stock_level}</td>
                <td>Rs. ${parseFloat(medicine.purchase_price).toFixed(2)}</td>
                <td>Rs. ${parseFloat(medicine.selling_price).toFixed(2)}</td>
                <td>${medicine.margin_percent}%</td>
                <td><span class="status status-${medicine.status.toLowerCase()}">${medicine.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editMedicine('${medicine.medicine_id}')">Edit</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteMedicine('${medicine.medicine_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStatistics() {
        const totalCount = this.medicines.length;
        const activeCount = this.medicines.filter(m => m.status === 'Active').length;
        const lowStockCount = this.medicines.filter(m => m.current_stock <= m.min_stock_level).length;
        const totalValue = this.medicines.reduce((sum, m) => sum + (m.current_stock * m.purchase_price), 0);

        document.getElementById('total-medicines-count').textContent = totalCount;
        document.getElementById('active-medicines-count').textContent = activeCount;
        document.getElementById('low-stock-count').textContent = lowStockCount;
        document.getElementById('total-inventory-value').textContent = `Rs. ${totalValue.toLocaleString()}`;
    }

    calculateMargin() {
        const purchasePrice = parseFloat(document.getElementById('purchase-price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling-price').value) || 0;
        
        if (purchasePrice > 0 && sellingPrice > 0) {
            const margin = ((sellingPrice - purchasePrice) / sellingPrice * 100).toFixed(2);
            document.getElementById('margin-percent').value = margin;
        } else {
            document.getElementById('margin-percent').value = '';
        }
    }

    async handleFormSubmit(event) {
        event.preventDefault();

        // Check if database is ready
        if (!dbManager.isReady()) {
            this.showAlert('Database is not ready. Please wait a moment and try again.', 'warning');
            return;
        }

        const formData = new FormData(event.target);
        const medicineData = Object.fromEntries(formData.entries());

        // Convert numeric fields
        medicineData.current_stock = parseInt(medicineData.current_stock) || 0;
        medicineData.min_stock_level = parseInt(medicineData.min_stock_level) || 0;
        medicineData.purchase_price = parseFloat(medicineData.purchase_price) || 0;
        medicineData.selling_price = parseFloat(medicineData.selling_price) || 0;
        medicineData.margin_percent = parseFloat(medicineData.margin_percent) || 0;

        try {
            if (medicineData.medicine_id) {
                // Update existing medicine
                await dbManager.update('medicines', medicineData);
                this.showAlert('Medicine updated successfully!', 'success');
            } else {
                // Add new medicine
                medicineData.medicine_id = await dbManager.generateId('medicines', 'MED');
                await dbManager.insert('medicines', medicineData);
                this.showAlert('Medicine added successfully!', 'success');
            }

            this.closeMedicineModal();
            await this.loadMedicines();
        } catch (error) {
            console.error('Error saving medicine:', error);
            this.showAlert('Error saving medicine. Please try again.', 'danger');
        }
    }

    showAlert(message, type) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        // Insert at the top of main content
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        // Auto-remove after 3 seconds
        setTimeout(() => alert.remove(), 3000);
    }

    closeMedicineModal() {
        document.getElementById('medicine-modal').style.display = 'none';
        document.getElementById('medicine-form').reset();
        document.getElementById('medicine-id').value = '';
        document.getElementById('modal-title').textContent = 'Add New Medicine';
    }
}

// Global functions
function showAddMedicineModal() {
    document.getElementById('medicine-modal').style.display = 'flex';
    document.getElementById('modal-title').textContent = 'Add New Medicine';
    document.getElementById('medicine-form').reset();
    document.getElementById('medicine-id').value = '';
}

function closeMedicineModal() {
    medicineManager.closeMedicineModal();
}

async function editMedicine(medicineId) {
    try {
        if (!dbManager.isReady()) {
            alert('Database is not ready. Please wait a moment and try again.');
            return;
        }

        const medicine = await dbManager.get('medicines', medicineId);
        if (!medicine) {
            alert('Medicine not found');
            return;
        }

        // Populate form with medicine data
        document.getElementById('medicine-id').value = medicine.medicine_id;
        document.getElementById('medicine-name').value = medicine.medicine_name;
        document.getElementById('generic-name').value = medicine.generic_name || '';
        document.getElementById('category').value = medicine.category;
        document.getElementById('manufacturer').value = medicine.manufacturer;
        document.getElementById('pack-size').value = medicine.pack_size || '';
        document.getElementById('unit-type').value = medicine.unit_type;
        document.getElementById('storage-location').value = medicine.storage_location || '';
        document.getElementById('purchase-price').value = medicine.purchase_price;
        document.getElementById('selling-price').value = medicine.selling_price;
        document.getElementById('margin-percent').value = medicine.margin_percent;
        document.getElementById('current-stock').value = medicine.current_stock;
        document.getElementById('min-stock-level').value = medicine.min_stock_level;
        document.getElementById('prescription-required').value = medicine.prescription_required;
        document.getElementById('status').value = medicine.status;

        // Show modal
        document.getElementById('medicine-modal').style.display = 'flex';
        document.getElementById('modal-title').textContent = 'Edit Medicine';
    } catch (error) {
        console.error('Error loading medicine for edit:', error);
        alert('Error loading medicine data');
    }
}

async function deleteMedicine(medicineId) {
    if (!confirm('Are you sure you want to delete this medicine? This action cannot be undone.')) {
        return;
    }

    try {
        if (!dbManager.isReady()) {
            alert('Database is not ready. Please wait a moment and try again.');
            return;
        }

        await dbManager.delete('medicines', medicineId);
        medicineManager.showAlert('Medicine deleted successfully!', 'success');
        await medicineManager.loadMedicines();
    } catch (error) {
        console.error('Error deleting medicine:', error);
        medicineManager.showAlert('Error deleting medicine. Please try again.', 'danger');
    }
}

function exportMedicines() {
    try {
        // Create CSV content
        const headers = [
            'Medicine ID', 'Medicine Name', 'Generic Name', 'Category', 'Manufacturer',
            'Pack Size', 'Unit Type', 'Current Stock', 'Min Stock Level',
            'Purchase Price', 'Selling Price', 'Margin %', 'Storage Location',
            'Prescription Required', 'Status'
        ];
        
        let csvContent = headers.join(',') + '\n';
        
        medicineManager.medicines.forEach(medicine => {
            const row = [
                medicine.medicine_id,
                `"${medicine.medicine_name}"`,
                `"${medicine.generic_name || ''}"`,
                `"${medicine.category}"`,
                `"${medicine.manufacturer}"`,
                `"${medicine.pack_size || ''}"`,
                `"${medicine.unit_type}"`,
                medicine.current_stock,
                medicine.min_stock_level,
                medicine.purchase_price,
                medicine.selling_price,
                medicine.margin_percent,
                `"${medicine.storage_location || ''}"`,
                medicine.prescription_required,
                medicine.status
            ];
            csvContent += row.join(',') + '\n';
        });
        
        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `medicines_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        medicineManager.showAlert('Medicines exported successfully!', 'success');
    } catch (error) {
        console.error('Error exporting medicines:', error);
        medicineManager.showAlert('Error exporting medicines. Please try again.', 'danger');
    }
}

// Initialize medicine manager when page loads
let medicineManager;
document.addEventListener('DOMContentLoaded', () => {
    medicineManager = new MedicineManager();
    // Export instance to global scope
    if (typeof window !== 'undefined') {
        window.medicineManager = medicineManager;
    }
});

// Export class for global use
if (typeof window !== 'undefined') {
    window.MedicineManager = MedicineManager;
}