// Utilities for Aanabi Pharmacy Management System v2.0
// Common functions to reduce code duplication and improve performance

const AanabiUtils = {
    // DOM manipulation utilities
    dom: {
        // Cached DOM elements for performance
        cache: new Map(),
        
        // Get element with caching
        get(id) {
            if (!this.cache.has(id)) {
                this.cache.set(id, document.getElementById(id));
            }
            return this.cache.get(id);
        },
        
        // Clear DOM cache
        clearCache() {
            this.cache.clear();
        },
        
        // Safe DOM manipulation
        setText(id, text) {
            const element = this.get(id);
            if (element) element.textContent = text;
        },
        
        setHTML(id, html) {
            const element = this.get(id);
            if (element) element.innerHTML = html;
        },
        
        show(id) {
            const element = this.get(id);
            if (element) element.style.display = 'block';
        },
        
        hide(id) {
            const element = this.get(id);
            if (element) element.style.display = 'none';
        },
        
        addClass(id, className) {
            const element = this.get(id);
            if (element) element.classList.add(className);
        },
        
        removeClass(id, className) {
            const element = this.get(id);
            if (element) element.classList.remove(className);
        }
    },

    // Loading state management
    loading: {
        show(elementId, message = 'Loading...') {
            const element = AanabiUtils.dom.get(elementId);
            if (element) {
                element.innerHTML = `
                    <div class="text-center">
                        <div class="loading-spinner" style="margin: 1rem auto;"></div>
                        <p class="text-muted">${message}</p>
                    </div>
                `;
            }
        },
        
        showGlobal(message = 'Loading...') {
            const overlay = AanabiUtils.dom.get('loading-overlay');
            if (overlay) {
                overlay.style.display = 'flex';
                overlay.innerHTML = `
                    <div style="text-align: center;">
                        <div class="loading-spinner"></div>
                        <p style="margin-top: 1rem; color: #666;">${message}</p>
                    </div>
                `;
            }
        },
        
        hide(elementId) {
            const element = AanabiUtils.dom.get(elementId);
            if (element) {
                element.innerHTML = '';
            }
        },
        
        hideGlobal() {
            const overlay = AanabiUtils.dom.get('loading-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        },
        
        setButtonLoading(buttonId, loading = true) {
            const button = AanabiUtils.dom.get(buttonId);
            if (button) {
                if (loading) {
                    button.classList.add('btn-loading');
                    button.disabled = true;
                } else {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                }
            }
        }
    },

    // Alert/notification utilities
    alerts: {
        show(message, type = 'info', containerId = 'alerts-container') {
            const container = AanabiUtils.dom.get(containerId);
            if (!container) return;
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = `
                <strong>${this.getIcon(type)}</strong> ${message}
                <button type="button" class="modal-close" onclick="this.parentElement.remove()" style="float: right; background: none; border: none;">&times;</button>
            `;
            
            container.innerHTML = '';
            container.appendChild(alertDiv);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        },
        
        getIcon(type) {
            const icons = {
                success: '✅',
                warning: '⚠️',
                danger: '❌',
                info: 'ℹ️'
            };
            return icons[type] || 'ℹ️';
        },
        
        showSuccess(message, containerId) {
            this.show(message, 'success', containerId);
        },
        
        showError(message, containerId) {
            this.show(message, 'danger', containerId);
        },
        
        showWarning(message, containerId) {
            this.show(message, 'warning', containerId);
        },
        
        showInfo(message, containerId) {
            this.show(message, 'info', containerId);
        }
    },

    // Data formatting utilities
    format: {
        currency(amount, currency = 'Rs.') {
            const num = parseFloat(amount || 0);
            return `${currency} ${num.toLocaleString('en-NP', { 
                minimumFractionDigits: 0,
                maximumFractionDigits: 2 
            })}`;
        },
        
        number(num) {
            return parseFloat(num || 0).toLocaleString();
        },
        
        percentage(value, total) {
            if (!total || total === 0) return '0%';
            return `${Math.round((value / total) * 100)}%`;
        },
        
        date(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        },
        
        time(timeString) {
            if (!timeString) return '';
            return new Date(`1970-01-01T${timeString}`).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        },
        
        datetime(dateTimeString) {
            if (!dateTimeString) return '';
            const date = new Date(dateTimeString);
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
    },

    // Animation utilities
    animation: {
        // Animate number value
        animateValue(elementId, start, end, duration = 1000, formatter = null) {
            const element = AanabiUtils.dom.get(elementId);
            if (!element) return;
            
            const range = end - start;
            const increment = range / (duration / 16);
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                    current = end;
                    clearInterval(timer);
                }
                
                const displayValue = formatter ? formatter(Math.round(current)) : Math.round(current);
                element.textContent = displayValue;
            }, 16);
        },
        
        // Fade in element
        fadeIn(elementId, duration = 300) {
            const element = AanabiUtils.dom.get(elementId);
            if (!element) return;
            
            element.style.opacity = '0';
            element.style.display = 'block';
            
            let opacity = 0;
            const increment = 1 / (duration / 16);
            
            const timer = setInterval(() => {
                opacity += increment;
                if (opacity >= 1) {
                    opacity = 1;
                    clearInterval(timer);
                }
                element.style.opacity = opacity;
            }, 16);
        },
        
        // Fade out element
        fadeOut(elementId, duration = 300) {
            const element = AanabiUtils.dom.get(elementId);
            if (!element) return;
            
            let opacity = 1;
            const decrement = 1 / (duration / 16);
            
            const timer = setInterval(() => {
                opacity -= decrement;
                if (opacity <= 0) {
                    opacity = 0;
                    element.style.display = 'none';
                    clearInterval(timer);
                }
                element.style.opacity = opacity;
            }, 16);
        }
    },

    // Date and time utilities
    datetime: {
        getCurrentDate() {
            return new Date().toISOString().split('T')[0];
        },
        
        getCurrentTime() {
            return new Date().toTimeString().split(' ')[0];
        },
        
        getCurrentDateTime() {
            return new Date().toISOString();
        },
        
        addDays(dateString, days) {
            const date = new Date(dateString);
            date.setDate(date.getDate() + days);
            return date.toISOString().split('T')[0];
        },
        
        getDaysDifference(date1, date2) {
            const d1 = new Date(date1);
            const d2 = new Date(date2);
            return Math.ceil((d2 - d1) / (1000 * 60 * 60 * 24));
        },
        
        isExpiringSoon(expiryDate, alertDays = 30) {
            const today = new Date();
            const expiry = new Date(expiryDate);
            const daysUntilExpiry = Math.ceil((expiry - today) / (1000 * 60 * 60 * 24));
            return daysUntilExpiry <= alertDays && daysUntilExpiry > 0;
        }
    },

    // Validation utilities
    validate: {
        email(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        phone(phone) {
            const re = /^[0-9]{10}$/;
            return re.test(phone.replace(/[^\d]/g, ''));
        },
        
        required(value) {
            return value !== null && value !== undefined && value.toString().trim() !== '';
        },
        
        number(value) {
            return !isNaN(value) && isFinite(value);
        },
        
        positiveNumber(value) {
            return this.number(value) && parseFloat(value) > 0;
        },
        
        percentage(value) {
            return this.number(value) && parseFloat(value) >= 0 && parseFloat(value) <= 100;
        }
    },

    // Local storage utilities with error handling
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.warn('Failed to save to localStorage:', error);
                return false;
            }
        },
        
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.warn('Failed to read from localStorage:', error);
                return defaultValue;
            }
        },
        
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.warn('Failed to remove from localStorage:', error);
                return false;
            }
        },
        
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.warn('Failed to clear localStorage:', error);
                return false;
            }
        }
    },

    // Table utilities
    table: {
        // Create table row HTML
        createRow(data, columns) {
            const cells = columns.map(col => {
                const value = data[col.field] || '';
                const formatted = col.formatter ? col.formatter(value, data) : value;
                return `<td>${formatted}</td>`;
            }).join('');
            
            return `<tr onclick="${columns.onClick || ''}">${cells}</tr>`;
        },
        
        // Update table with data
        updateTable(tableBodyId, data, columns) {
            const tbody = AanabiUtils.dom.get(tableBodyId);
            if (!tbody) return;
            
            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="${columns.length}" class="text-center text-muted">
                            No data available
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = data.map(item => this.createRow(item, columns)).join('');
        },
        
        // Show loading in table
        showLoading(tableBodyId, columnCount) {
            const tbody = AanabiUtils.dom.get(tableBodyId);
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="${columnCount}" class="text-center">
                            <div class="loading-spinner" style="margin: 1rem auto;"></div>
                            Loading...
                        </td>
                    </tr>
                `;
            }
        }
    },

    // Performance monitoring utilities
    performance: {
        timers: new Map(),
        
        start(label) {
            this.timers.set(label, performance.now());
        },
        
        end(label) {
            const start = this.timers.get(label);
            if (start) {
                const duration = performance.now() - start;
                console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
                this.timers.delete(label);
                return duration;
            }
            return 0;
        },
        
        measure(fn, label) {
            this.start(label);
            const result = fn();
            this.end(label);
            return result;
        },
        
        async measureAsync(fn, label) {
            this.start(label);
            const result = await fn();
            this.end(label);
            return result;
        }
    },

    // Error handling utilities
    error: {
        handle(error, context = 'System') {
            console.error(`${context} Error:`, error);
            
            // Show user-friendly error message
            const message = this.getUserFriendlyMessage(error);
            AanabiUtils.alerts.showError(message);
        },
        
        getUserFriendlyMessage(error) {
            if (error.message.includes('Database')) {
                return 'Database error occurred. Please try again.';
            } else if (error.message.includes('Network')) {
                return 'Network error. Please check your connection.';
            } else if (error.message.includes('Permission')) {
                return 'You do not have permission to perform this action.';
            } else {
                return 'An unexpected error occurred. Please try again.';
            }
        },
        
        // Wrap async functions with error handling
        async withErrorHandling(fn, context = 'Operation') {
            try {
                return await fn();
            } catch (error) {
                this.handle(error, context);
                throw error;
            }
        }
    },

    // Debounce utility for search and other frequent operations
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle utility for scroll and resize events
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Deep clone utility
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    // URL parameter utilities
    url: {
        getParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        },
        
        setParam(name, value) {
            const url = new URL(window.location);
            url.searchParams.set(name, value);
            window.history.pushState({}, '', url);
        },
        
        removeParam(name) {
            const url = new URL(window.location);
            url.searchParams.delete(name);
            window.history.pushState({}, '', url);
        }
    }
};

// Export for global use
if (typeof window !== 'undefined') {
    window.AanabiUtils = AanabiUtils;
}

// Initialize global error handling
window.addEventListener('error', (event) => {
    AanabiUtils.error.handle(event.error, 'Global');
});

window.addEventListener('unhandledrejection', (event) => {
    AanabiUtils.error.handle(event.reason, 'Promise');
});

console.log('✅ Aanabi utilities loaded successfully');
